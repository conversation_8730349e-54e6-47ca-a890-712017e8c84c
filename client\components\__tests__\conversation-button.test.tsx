import { render, screen, fireEvent } from '@testing-library/react'
import { ConversationButton } from '../conversation/conversation-button'

// Mock the stores and hooks
jest.mock('@/store/app-store', () => ({
  useAppStore: () => ({
    isConversationActive: false,
    setConversationActive: jest.fn(),
    hasAudioPermission: true,
    connectionStatus: 'connected',
  }),
}))

jest.mock('@/hooks/use-audio-recording', () => ({
  useAudioRecording: () => ({
    handleConversationStateChange: jest.fn(),
  }),
}))

jest.mock('@/hooks/use-websocket-manager', () => ({
  useWebSocketManager: () => ({
    isConnected: true,
  }),
}))

describe('ConversationButton', () => {
  it('should render start button when conversation is inactive', () => {
    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Start Conversation')
  })

  it('should render stop button when conversation is active', () => {
    // Mock active conversation state
    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: true,
      setConversationActive: jest.fn(),
      hasAudioPermission: true,
      connectionStatus: 'connected',
    })

    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Stop Conversation')
  })

  it('should be disabled when not connected', () => {
    // Mock disconnected state
    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: jest.fn(),
      hasAudioPermission: true,
      connectionStatus: 'disconnected',
    })

    jest.mocked(require('@/hooks/use-websocket-manager').useWebSocketManager).mockReturnValue({
      isConnected: false,
    })

    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('should be disabled when no audio permission', () => {
    // Mock no audio permission
    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: jest.fn(),
      hasAudioPermission: false,
      connectionStatus: 'connected',
    })

    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
  })

  it('should handle click to start conversation', () => {
    const mockSetConversationActive = jest.fn()
    const mockHandleConversationStateChange = jest.fn()

    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: mockSetConversationActive,
      hasAudioPermission: true,
      connectionStatus: 'connected',
    })

    jest.mocked(require('@/hooks/use-audio-recording').useAudioRecording).mockReturnValue({
      handleConversationStateChange: mockHandleConversationStateChange,
    })

    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(mockSetConversationActive).toHaveBeenCalledWith(true)
    expect(mockHandleConversationStateChange).toHaveBeenCalledWith(true)
  })

  it('should handle click to stop conversation', () => {
    const mockSetConversationActive = jest.fn()
    const mockHandleConversationStateChange = jest.fn()

    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: true,
      setConversationActive: mockSetConversationActive,
      hasAudioPermission: true,
      connectionStatus: 'connected',
    })

    jest.mocked(require('@/hooks/use-audio-recording').useAudioRecording).mockReturnValue({
      handleConversationStateChange: mockHandleConversationStateChange,
    })

    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(mockSetConversationActive).toHaveBeenCalledWith(false)
    expect(mockHandleConversationStateChange).toHaveBeenCalledWith(false)
  })

  it('should show correct icon for start state', () => {
    render(<ConversationButton />)
    
    // Check for microphone icon (start state)
    const micIcon = screen.getByTestId('mic-icon') || screen.getByRole('button').querySelector('svg')
    expect(micIcon).toBeInTheDocument()
  })

  it('should show correct icon for stop state', () => {
    jest.mocked(require('@/store/app-store').useAppStore).mockReturnValue({
      isConversationActive: true,
      setConversationActive: jest.fn(),
      hasAudioPermission: true,
      connectionStatus: 'connected',
    })

    render(<ConversationButton />)
    
    // Check for stop icon (stop state)
    const stopIcon = screen.getByTestId('stop-icon') || screen.getByRole('button').querySelector('svg')
    expect(stopIcon).toBeInTheDocument()
  })

  it('should have proper accessibility attributes', () => {
    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label')
    expect(button).toHaveAttribute('type', 'button')
  })

  it('should show loading state during transition', () => {
    // This would require mocking a loading state in the component
    // For now, we'll just verify the button exists
    render(<ConversationButton />)
    
    const button = screen.getByRole('button')
    expect(button).toBeInTheDocument()
  })
})
