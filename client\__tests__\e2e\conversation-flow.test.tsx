import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { AudioManusApp } from '../../components/audio-manus-app'

// Mock all external dependencies
jest.mock('../../hooks/use-audio-manager', () => ({
  useAudioManager: () => ({
    requestMicrophonePermission: jest.fn().mockResolvedValue(true),
    startRecording: jest.fn(),
    stopRecording: jest.fn(),
    isRecording: false,
    audioContext: {},
    initializeAudioWorklet: jest.fn().mockResolvedValue({}),
    workletNode: null,
  }),
}))

jest.mock('../../hooks/use-websocket-manager', () => ({
  useWebSocketManager: () => ({
    connect: jest.fn(),
    disconnect: jest.fn(),
    sendAudioData: jest.fn(),
    sendModeSelection: jest.fn().mockResolvedValue(true),
    sendManualSpeechEnd: jest.fn(),
    isConnected: true,
    currentSession: {
      sessionId: 'test-session-123',
      mode: 'standard',
      isActive: true,
    },
  }),
}))

jest.mock('../../hooks/use-audio-recording', () => ({
  useAudioRecording: () => ({
    handleConversationStateChange: jest.fn(),
  }),
}))

jest.mock('../../hooks/use-service-worker', () => ({
  useServiceWorker: () => ({
    isSupported: true,
    isRegistered: true,
    canInstall: false,
    isPWA: false,
    registerServiceWorker: jest.fn(),
    showInstallPrompt: jest.fn(),
  }),
}))

// Mock stores
jest.mock('../../store/app-store', () => ({
  useAppStore: () => ({
    isConversationActive: false,
    setConversationActive: jest.fn(),
    hasAudioPermission: true,
    connectionStatus: 'connected',
    conversationMode: 'standard',
    setConversationMode: jest.fn(),
    audioVolume: 0.5,
    setAudioVolume: jest.fn(),
    updateTranscriptStatus: jest.fn(),
    updateAIStatus: jest.fn(),
    clearTranscriptStatus: jest.fn(),
    clearAIStatus: jest.fn(),
  }),
}))

jest.mock('../../store/chat-store', () => ({
  useChatStore: () => ({
    messages: [
      {
        id: '1',
        role: 'user',
        content: 'Hello, how are you?',
        timestamp: new Date(),
      },
      {
        id: '2',
        role: 'assistant',
        content: 'I am doing well, thank you for asking!',
        timestamp: new Date(),
      },
    ],
    addMessage: jest.fn(),
    clearMessages: jest.fn(),
    updateStreamingMessage: jest.fn(),
    completeStreamingMessage: jest.fn(),
    currentStreamingMessageId: null,
  }),
}))

describe('End-to-End Conversation Flow', () => {
  const user = userEvent.setup()

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks()
  })

  it('should render the complete application', async () => {
    render(<AudioManusApp />)

    // Check for main components
    expect(screen.getByText('Audio Agent')).toBeInTheDocument()
    expect(screen.getByText('Conversation')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /start conversation/i })).toBeInTheDocument()
  })

  it('should handle mode selection', async () => {
    const mockSetConversationMode = jest.fn()
    const mockSendModeSelection = jest.fn().mockResolvedValue(true)

    jest.mocked(require('../../store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: jest.fn(),
      hasAudioPermission: true,
      connectionStatus: 'connected',
      conversationMode: 'standard',
      setConversationMode: mockSetConversationMode,
      audioVolume: 0.5,
      setAudioVolume: jest.fn(),
      updateTranscriptStatus: jest.fn(),
      updateAIStatus: jest.fn(),
      clearTranscriptStatus: jest.fn(),
      clearAIStatus: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-websocket-manager').useWebSocketManager).mockReturnValue({
      connect: jest.fn(),
      disconnect: jest.fn(),
      sendAudioData: jest.fn(),
      sendModeSelection: mockSendModeSelection,
      sendManualSpeechEnd: jest.fn(),
      isConnected: true,
      currentSession: null,
    })

    render(<AudioManusApp />)

    // Find and click interviewer mode
    const interviewerButton = screen.getByText('面试官模式')
    await user.click(interviewerButton)

    await waitFor(() => {
      expect(mockSetConversationMode).toHaveBeenCalledWith('interviewer')
      expect(mockSendModeSelection).toHaveBeenCalledWith('interviewer')
    })
  })

  it('should handle conversation start/stop', async () => {
    const mockSetConversationActive = jest.fn()
    const mockHandleConversationStateChange = jest.fn()

    jest.mocked(require('../../store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: mockSetConversationActive,
      hasAudioPermission: true,
      connectionStatus: 'connected',
      conversationMode: 'standard',
      setConversationMode: jest.fn(),
      audioVolume: 0.5,
      setAudioVolume: jest.fn(),
      updateTranscriptStatus: jest.fn(),
      updateAIStatus: jest.fn(),
      clearTranscriptStatus: jest.fn(),
      clearAIStatus: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-audio-recording').useAudioRecording).mockReturnValue({
      handleConversationStateChange: mockHandleConversationStateChange,
    })

    render(<AudioManusApp />)

    // Find and click start conversation button
    const startButton = screen.getByRole('button', { name: /start conversation/i })
    await user.click(startButton)

    expect(mockSetConversationActive).toHaveBeenCalledWith(true)
    expect(mockHandleConversationStateChange).toHaveBeenCalledWith(true)
  })

  it('should display chat messages', async () => {
    render(<AudioManusApp />)

    // Check for chat messages
    expect(screen.getByText('Hello, how are you?')).toBeInTheDocument()
    expect(screen.getByText('I am doing well, thank you for asking!')).toBeInTheDocument()
  })

  it('should handle clear chat functionality', async () => {
    const mockClearMessages = jest.fn()

    jest.mocked(require('../../store/chat-store').useChatStore).mockReturnValue({
      messages: [
        {
          id: '1',
          role: 'user',
          content: 'Hello, how are you?',
          timestamp: new Date(),
        },
      ],
      addMessage: jest.fn(),
      clearMessages: mockClearMessages,
      updateStreamingMessage: jest.fn(),
      completeStreamingMessage: jest.fn(),
      currentStreamingMessageId: null,
    })

    render(<AudioManusApp />)

    // Find and click clear chat button
    const clearButton = screen.getByRole('button', { name: /clear/i })
    await user.click(clearButton)

    expect(mockClearMessages).toHaveBeenCalled()
  })

  it('should show connection status', async () => {
    render(<AudioManusApp />)

    // Check for connection status indicator
    expect(screen.getByText('Connected')).toBeInTheDocument()
  })

  it('should handle disconnected state', async () => {
    jest.mocked(require('../../store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: jest.fn(),
      hasAudioPermission: true,
      connectionStatus: 'disconnected',
      conversationMode: 'standard',
      setConversationMode: jest.fn(),
      audioVolume: 0.5,
      setAudioVolume: jest.fn(),
      updateTranscriptStatus: jest.fn(),
      updateAIStatus: jest.fn(),
      clearTranscriptStatus: jest.fn(),
      clearAIStatus: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-websocket-manager').useWebSocketManager).mockReturnValue({
      connect: jest.fn(),
      disconnect: jest.fn(),
      sendAudioData: jest.fn(),
      sendModeSelection: jest.fn(),
      sendManualSpeechEnd: jest.fn(),
      isConnected: false,
      currentSession: null,
    })

    render(<AudioManusApp />)

    // Conversation button should be disabled when disconnected
    const startButton = screen.getByRole('button', { name: /start conversation/i })
    expect(startButton).toBeDisabled()
  })

  it('should handle no audio permission state', async () => {
    jest.mocked(require('../../store/app-store').useAppStore).mockReturnValue({
      isConversationActive: false,
      setConversationActive: jest.fn(),
      hasAudioPermission: false,
      connectionStatus: 'connected',
      conversationMode: 'standard',
      setConversationMode: jest.fn(),
      audioVolume: 0.5,
      setAudioVolume: jest.fn(),
      updateTranscriptStatus: jest.fn(),
      updateAIStatus: jest.fn(),
      clearTranscriptStatus: jest.fn(),
      clearAIStatus: jest.fn(),
    })

    render(<AudioManusApp />)

    // Conversation button should be disabled without audio permission
    const startButton = screen.getByRole('button', { name: /start conversation/i })
    expect(startButton).toBeDisabled()
  })

  it('should display welcome message for new users', async () => {
    // Mock empty chat for new users
    jest.mocked(require('../../store/chat-store').useChatStore).mockReturnValue({
      messages: [],
      addMessage: jest.fn(),
      clearMessages: jest.fn(),
      updateStreamingMessage: jest.fn(),
      completeStreamingMessage: jest.fn(),
      currentStreamingMessageId: null,
    })

    render(<AudioManusApp />)

    // Check for welcome message
    expect(screen.getByText('欢迎使用 Audio Agent')).toBeInTheDocument()
    expect(screen.getByText('系统状态检查')).toBeInTheDocument()
  })

  it('should show audio visualization', async () => {
    render(<AudioManusApp />)

    // Check for audio visualizer (canvas or waveform container)
    const audioVisualizer = screen.getByRole('region', { name: /audio/i }) || 
                           document.querySelector('canvas') ||
                           document.querySelector('[class*="waveform"]')
    
    expect(audioVisualizer).toBeInTheDocument()
  })

  it('should display performance information', async () => {
    render(<AudioManusApp />)

    // Check for performance display
    expect(screen.getByText('Audio Information')).toBeInTheDocument()
  })
})
