"use client"

import { BackgroundGradient } from "@/components/ui/background-gradient"
import { ChatMessages } from "@/components/chat/chat-messages"
import { useChatStore } from "@/store/chat-store"
import { memo } from "react"
import { MessageCircle, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

const ConversationIcon = memo(() => (
  <div className="p-2 rounded-xl bg-gradient-to-br from-orange-500 to-amber-600 text-white shadow-lg">
    <MessageCircle size={20} />
  </div>
))

ConversationIcon.displayName = "ConversationIcon"

export const ChatInterface = memo(() => {
  const { messages, clearMessages, addMessage } = useChatStore()
  const t = useTranslations('chatInterface')

  const handleClearChat = () => {
    if (messages.length === 0) {
      toast.info(t('chatAlreadyEmpty'))
      return
    }

    const count = messages.length
    clearMessages()
    toast.success(t('clearedMessages', { count }))
  }

  // Debug function to test message display
  const handleTestMessage = () => {
    addMessage({
      role: "assistant",
      content: "This is a test message to verify UI is working",
      isStreaming: false,
      messageKey: `test_${Date.now()}`
    })
    toast.success("Test message added")
  }

  return (
    <BackgroundGradient className="clay-card p-6 h-[600px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <ConversationIcon />
          <h2 className="text-xl font-bold bg-gradient-to-r from-orange-600 to-amber-600 bg-clip-text text-transparent">
            {t('title')}
          </h2>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <div className="text-sm text-muted-foreground">
              {messages.length} {messages.length !== 1 ? t('messageCountPlural') : t('messageCount')}
            </div>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleTestMessage}
            className="h-8 px-2 text-muted-foreground hover:text-blue-600"
            title="Add test message"
          >
            Test
          </Button>

          {messages.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearChat}
              className="h-8 px-2 text-muted-foreground hover:text-destructive"
              title={t('clearAllMessages')}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <ChatMessages />
      </div>
    </BackgroundGradient>
  )
})

ChatInterface.displayName = "ChatInterface"
