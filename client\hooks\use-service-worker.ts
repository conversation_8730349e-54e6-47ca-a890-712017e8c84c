"use client"

import { useEffect, useCallback, useState } from "react"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

export const useServiceWorker = () => {
  const [isSupported, setIsSupported] = useState(false)
  const [isRegistered, setIsRegistered] = useState(false)
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null)
  const [updateAvailable, setUpdateAvailable] = useState(false)
  const t = useTranslations('toast')

  const registerServiceWorker = useCallback(async () => {
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      console.log('Service Worker not supported or running on server')
      return false
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none'
      })

      console.log('Service Worker registered successfully:', registration)
      setRegistration(registration)
      setIsRegistered(true)

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('New service worker available')
              setUpdateAvailable(true)
              toast.info(t('appUpdateAvailable'), {
                duration: 10000,
                action: {
                  label: t('refresh'),
                  onClick: () => typeof window !== 'undefined' && window.location.reload()
                }
              })
            }
          })
        }
      })

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        console.log('Message from service worker:', event.data)
        
        if (event.data.type === 'PROCESS_MESSAGE_QUEUE') {
          // Trigger message queue processing in WebSocket manager
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('sw-process-queue'))
          }
        }
      })

      return true
    } catch (error) {
      console.error('Service Worker registration failed:', error)
      toast.error('Failed to register service worker')
      return false
    }
  }, [])

  const unregisterServiceWorker = useCallback(async () => {
    if (!registration) return false

    try {
      const result = await registration.unregister()
      console.log('Service Worker unregistered:', result)
      setIsRegistered(false)
      setRegistration(null)
      toast.success('Service worker unregistered')
      return result
    } catch (error) {
      console.error('Service Worker unregistration failed:', error)
      toast.error('Failed to unregister service worker')
      return false
    }
  }, [registration])

  const updateServiceWorker = useCallback(async () => {
    if (!registration) return false

    try {
      await registration.update()
      console.log('Service Worker update check completed')
      return true
    } catch (error) {
      console.error('Service Worker update failed:', error)
      return false
    }
  }, [registration])

  const skipWaiting = useCallback(async () => {
    if (!registration?.waiting) return false

    try {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      console.log('Service Worker skip waiting requested')
      return true
    } catch (error) {
      console.error('Service Worker skip waiting failed:', error)
      return false
    }
  }, [registration])

  // Background sync for message queue
  const requestBackgroundSync = useCallback(async (tag: string = 'background-sync-messages') => {
    if (typeof window === 'undefined' || !registration || !('sync' in window.ServiceWorkerRegistration.prototype)) {
      console.log('Background Sync not supported or running on server')
      return false
    }

    try {
      await registration.sync.register(tag)
      console.log('Background sync registered:', tag)
      return true
    } catch (error) {
      console.error('Background sync registration failed:', error)
      return false
    }
  }, [registration])

  // Check if app is running as PWA
  const isPWA = useCallback(() => {
    if (typeof window === 'undefined') return false
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true ||
           document.referrer.includes('android-app://')
  }, [])

  // Install prompt handling
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null)
  const [canInstall, setCanInstall] = useState(false)

  const showInstallPrompt = useCallback(async () => {
    if (!deferredPrompt) return false

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      console.log('Install prompt outcome:', outcome)
      
      if (outcome === 'accepted') {
        toast.success('App installed successfully!')
      }
      
      setDeferredPrompt(null)
      setCanInstall(false)
      return outcome === 'accepted'
    } catch (error) {
      console.error('Install prompt failed:', error)
      return false
    }
  }, [deferredPrompt])

  useEffect(() => {
    setIsSupported(typeof window !== 'undefined' && 'serviceWorker' in navigator)

    // Handle install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setCanInstall(true)
      console.log('Install prompt available')
    }

    const handleAppInstalled = () => {
      console.log('App installed')
      setCanInstall(false)
      setDeferredPrompt(null)
      toast.success('Audio Agent installed successfully!')
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.addEventListener('appinstalled', handleAppInstalled)
    }

    // Auto-register service worker
    if (isSupported) {
      registerServiceWorker()
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
        window.removeEventListener('appinstalled', handleAppInstalled)
      }
    }
  }, [isSupported, registerServiceWorker])

  return {
    isSupported,
    isRegistered,
    registration,
    updateAvailable,
    canInstall,
    isPWA: isPWA(),
    registerServiceWorker,
    unregisterServiceWorker,
    updateServiceWorker,
    skipWaiting,
    requestBackgroundSync,
    showInstallPrompt
  }
}
