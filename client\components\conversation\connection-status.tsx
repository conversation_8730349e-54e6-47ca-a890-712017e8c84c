"use client"

import { motion } from "framer-motion"
import { Wifi, WifiOff, Loader2 } from "lucide-react"
import { useAppStore } from "@/store/app-store"

export function ConnectionStatus() {
  const { connectionStatus } = useAppStore()

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case "connected":
        return {
          icon: Wifi,
          text: "Connected",
          className: "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900",
        }
      case "connecting":
        return {
          icon: Loader2,
          text: "Connecting",
          className: "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900",
        }
      case "disconnected":
        return {
          icon: WifiOff,
          text: "Disconnected",
          className: "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900",
        }
      case "error":
        return {
          icon: WifiOff,
          text: "Error",
          className: "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900",
        }
      default:
        return {
          icon: WifiOff,
          text: "Unknown",
          className: "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900",
        }
    }
  }

  const { icon: Icon, text, className } = getStatusConfig()

  return (
    <motion.div
      className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium ${className}`}
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      <Icon className={`w-4 h-4 ${connectionStatus === "connecting" ? "animate-spin" : ""}`} />
      <span>{text}</span>
    </motion.div>
  )
}
