"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON>ap, Briefcase } from "lucide-react"
import { useAppStore } from "@/store/app-store"
import { useChatStore } from "@/store/chat-store"

interface WelcomeMessageProps {
  className?: string
}

/**
 * Welcome message component - now disabled to remove UI clutter
 */
export function WelcomeMessage({ className = "" }: WelcomeMessageProps) {
  // Component disabled - return null to not render anything
  return null
}
