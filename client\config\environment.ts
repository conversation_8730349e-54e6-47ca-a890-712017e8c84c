/**
 * Environment-based configuration management for Audio Agent
 * Supports development, staging, and production environments
 */

export interface AudioAgentConfig {
  // WebSocket Configuration
  websocket: {
    url: string
    reconnectAttempts: number
    reconnectDelay: number
    heartbeatInterval: number
    connectionTimeout: number
  }

  // Audio Configuration
  audio: {
    sampleRate: number
    chunkSize: number
    format: 'pcm16' | 'webm' | 'mp3'
    enableWorklet: boolean
    workletPath: string
    defaultGain: number
    defaultNoiseGate: number
    defaultCompression: number
  }

  // Performance Configuration
  performance: {
    maxMessageQueueSize: number
    maxAudioChunks: number
    chunkTimeout: number
    cleanupInterval: number
    enablePerformanceMonitoring: boolean
  }

  // UI Configuration
  ui: {
    theme: 'light' | 'dark' | 'system'
    enableAnimations: boolean
    enableDebugPanel: boolean
    showPerformanceMetrics: boolean
    autoScrollChat: boolean
  }

  // Feature Flags
  features: {
    enableWaveSurfer: boolean
    enableServiceWorker: boolean
    enablePWA: boolean
    enableAdvancedAudio: boolean
    enableTranscriptionBuffer: boolean
    enableErrorRecovery: boolean
  }

  // API Configuration
  api: {
    baseUrl: string
    timeout: number
    retryAttempts: number
  }

  // Logging Configuration
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    enableConsole: boolean
    enableRemote: boolean
    remoteEndpoint?: string
  }
}

// Default configuration
const defaultConfig: AudioAgentConfig = {
  websocket: {
    url: 'ws://localhost:8000/ws/conversation',
    reconnectAttempts: 5,
    reconnectDelay: 1000,
    heartbeatInterval: 30000,
    connectionTimeout: 10000,
  },

  audio: {
    sampleRate: 24000,
    chunkSize: 2048, // Reduced for lower latency
    format: 'pcm16',
    enableWorklet: true,
    workletPath: '/audio-worklet-processor.js',
    defaultGain: 1.0,
    defaultNoiseGate: 0.01,
    defaultCompression: 2.0,
  },

  performance: {
    maxMessageQueueSize: 100,
    maxAudioChunks: 1000,
    chunkTimeout: 30000,
    cleanupInterval: 10000,
    enablePerformanceMonitoring: true,
  },

  ui: {
    theme: 'system',
    enableAnimations: true,
    enableDebugPanel: true,
    showPerformanceMetrics: true,
    autoScrollChat: true,
  },

  features: {
    enableWaveSurfer: true,
    enableServiceWorker: true,
    enablePWA: true,
    enableAdvancedAudio: true,
    enableTranscriptionBuffer: true,
    enableErrorRecovery: true,
  },

  api: {
    baseUrl: 'http://localhost:8000',
    timeout: 30000,
    retryAttempts: 3,
  },

  logging: {
    level: 'info',
    enableConsole: true,
    enableRemote: false,
  },
}

// Environment-specific overrides
const environmentConfigs: Record<string, Partial<AudioAgentConfig>> = {
  development: {
    websocket: {
      url: 'ws://localhost:8000/ws/conversation',
    },
    logging: {
      level: 'debug',
      enableConsole: true,
    },
    ui: {
      enableDebugPanel: true,
      showPerformanceMetrics: true,
    },
    features: {
      enableWaveSurfer: true,
      enableAdvancedAudio: true,
    },
  },

  staging: {
    websocket: {
      url: process.env.NEXT_PUBLIC_WS_URL || 'wss://staging-api.audioagent.com/ws/conversation',
    },
    api: {
      baseUrl: process.env.NEXT_PUBLIC_API_URL || 'https://staging-api.audioagent.com',
    },
    logging: {
      level: 'info',
      enableConsole: false,
      enableRemote: true,
      remoteEndpoint: 'https://staging-api.audioagent.com/logs',
    },
    ui: {
      enableDebugPanel: false,
      showPerformanceMetrics: false,
    },
  },

  production: {
    websocket: {
      url: process.env.NEXT_PUBLIC_WS_URL || 'wss://api.audioagent.com/ws/conversation',
      reconnectAttempts: 10,
      reconnectDelay: 2000,
    },
    api: {
      baseUrl: process.env.NEXT_PUBLIC_API_URL || 'https://api.audioagent.com',
      timeout: 60000,
      retryAttempts: 5,
    },
    logging: {
      level: 'warn',
      enableConsole: false,
      enableRemote: true,
      remoteEndpoint: 'https://api.audioagent.com/logs',
    },
    ui: {
      enableDebugPanel: false,
      showPerformanceMetrics: false,
      enableAnimations: true,
    },
    performance: {
      enablePerformanceMonitoring: false,
    },
  },
}

// Get current environment
const getCurrentEnvironment = (): string => {
  if (typeof window === 'undefined') {
    return process.env.NODE_ENV || 'development'
  }
  
  return process.env.NODE_ENV || 'development'
}

// Deep merge configuration objects
const deepMerge = (target: any, source: any): any => {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(target[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }
  
  return result
}

// Create final configuration
const createConfig = (): AudioAgentConfig => {
  const environment = getCurrentEnvironment()
  const envConfig = environmentConfigs[environment] || {}
  
  return deepMerge(defaultConfig, envConfig) as AudioAgentConfig
}

// Export the configuration
export const config = createConfig()

// Export environment utilities
export const isDevelopment = () => getCurrentEnvironment() === 'development'
export const isProduction = () => getCurrentEnvironment() === 'production'
export const isStaging = () => getCurrentEnvironment() === 'staging'

// Configuration validation
export const validateConfig = (cfg: AudioAgentConfig): boolean => {
  try {
    // Validate required fields
    if (!cfg.websocket.url) return false
    if (!cfg.api.baseUrl) return false
    if (cfg.audio.sampleRate <= 0) return false
    if (cfg.performance.maxMessageQueueSize <= 0) return false
    
    return true
  } catch (error) {
    console.error('Configuration validation failed:', error)
    return false
  }
}

// Log configuration on load (development only)
if (isDevelopment()) {
  console.log('🔧 Audio Agent Configuration:', config)
  console.log('✅ Configuration valid:', validateConfig(config))
}
