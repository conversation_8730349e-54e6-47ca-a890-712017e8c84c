/**
 * Audio Manager for Audio Agent Client
 * Handles audio recording and playback functionality
 *
 * 重构版本：统一音频播放接口，支持直接音频通道
 */

/**
 * 简化的音频播放器 - 专门处理PCM16音频流
 */
class DirectAudioPlayer {
  constructor(sampleRate = 24000) {
    this.sampleRate = sampleRate;
    this.isPlaying = false;
    this.audioContext = null;
    this.currentSource = null;
    this.gainNode = null;
    this.playbackQueue = [];
    this.interruptFlag = false;
    this.audioContextState = "uninitialized";

    // 🎯 新增：流式播放队列管理
    this.audioQueue = [];
    this.isProcessingQueue = false;
    this.nextScheduledTime = 0;
    this.bufferDuration = 0.1; // 100ms缓冲

    // 🎯 修复：跟踪所有活跃的音频源
    this.activeSources = new Set();

    // 回调函数
    this.onStart = null;
    this.onStop = null;
    this.onError = null;

    // 错误统计
    this.errorCount = 0;
    this.maxRetries = 3;

    // 🎯 新增：去重管理
    this.processedChunks = new Set();
    this.maxProcessedChunks = 1000;
  }

  /**
   * 初始化音频播放器
   */
  async initialize(audioContext) {
    try {
      this.audioContext = audioContext;
      this.audioContextState = this.audioContext.state;

      // 创建音量控制节点
      this.gainNode = this.audioContext.createGain();
      this.gainNode.gain.value = 1.0;
      this.gainNode.connect(this.audioContext.destination);

      // 监听AudioContext状态变化
      this.audioContext.addEventListener("statechange", () => {
        this.audioContextState = this.audioContext.state;
        console.log(
          `🎵 AudioContext state changed to: ${this.audioContextState}`
        );
      });

      // 🎯 初始化队列处理
      this.nextScheduledTime = this.audioContext.currentTime;

      console.log(
        `🎵 DirectAudioPlayer initialized (state: ${this.audioContextState})`
      );
      return true;
    } catch (error) {
      console.error("❌ Failed to initialize DirectAudioPlayer:", error);
      this.onError?.(error);
      return false;
    }
  }

  /**
   * 确保AudioContext处于运行状态
   */
  async ensureAudioContextRunning() {
    if (!this.audioContext) {
      throw new Error("AudioContext not initialized");
    }

    if (this.audioContext.state === "suspended") {
      console.log("🎵 Resuming suspended AudioContext...");
      try {
        await this.audioContext.resume();
        this.audioContextState = this.audioContext.state;
        console.log(
          `🎵 AudioContext resumed (state: ${this.audioContextState})`
        );
      } catch (error) {
        console.error("❌ Failed to resume AudioContext:", error);
        throw error;
      }
    }

    if (this.audioContext.state !== "running") {
      console.warn(
        `⚠️ AudioContext state is ${this.audioContext.state}, but proceeding anyway`
      );
    }
  }

  /**
   * 播放PCM16音频数据 - 流式队列版本
   * @param {string} base64Data - base64编码的PCM16数据
   * @param {string} chunkId - 可选的分块ID用于去重
   */
  async playChunk(base64Data, chunkId = null) {
    if (!base64Data || this.interruptFlag) {
      return;
    }

    try {
      // 🎯 去重检查
      if (chunkId) {
        if (this.processedChunks.has(chunkId)) {
          console.debug(`🔄 跳过重复音频块: ${chunkId}`);
          return;
        }
        this.processedChunks.add(chunkId);

        // 清理过期的去重记录
        if (this.processedChunks.size > this.maxProcessedChunks) {
          const chunksArray = Array.from(this.processedChunks);
          this.processedChunks = new Set(chunksArray.slice(-500));
        }
      }

      // 确保AudioContext处于运行状态
      await this.ensureAudioContextRunning();

      // 验证和处理音频数据
      const audioBuffer = await this.processAudioData(base64Data);
      if (!audioBuffer) {
        return;
      }

      // 🎯 添加到队列而不是直接播放
      this.audioQueue.push({
        buffer: audioBuffer,
        chunkId: chunkId,
        timestamp: Date.now(),
      });

      // 🎯 修复：立即设置播放状态，确保语音中断检测能正确工作
      if (!this.isPlaying) {
        this.isPlaying = true;
        this.onStart?.();
      }

      // 启动队列处理
      this.processAudioQueue();

      // 重置错误计数
      this.errorCount = 0;
      console.debug(
        `✅ Audio chunk queued: ${audioBuffer.duration.toFixed(3)}s`
      );
    } catch (error) {
      this.errorCount++;
      console.error(
        `❌ DirectAudioPlayer playback error (attempt ${this.errorCount}):`,
        error
      );

      if (this.errorCount < this.maxRetries) {
        console.log(
          `🔄 Retrying audio playback (${this.errorCount}/${this.maxRetries})`
        );
        // 短暂延迟后重试
        setTimeout(() => {
          this.playChunk(base64Data, chunkId);
        }, 100 * this.errorCount);
      } else {
        console.error(
          `❌ Audio playback failed after ${this.maxRetries} attempts`
        );
        this.onError?.(error);
        this.errorCount = 0; // 重置错误计数
      }
    }
  }

  /**
   * 🎯 新增：处理音频队列
   */
  async processAudioQueue() {
    if (this.isProcessingQueue || this.audioQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.audioQueue.length > 0 && !this.interruptFlag) {
        const audioItem = this.audioQueue.shift();
        const { buffer, chunkId } = audioItem;

        // --- START OF FIX: 增加前瞻缓冲 ---
        // 增加一个 50ms 的缓冲，给浏览器更充足的调度时间
        const lookAheadTime = 0.05;
        const startTime = Math.max(
          this.nextScheduledTime,
          this.audioContext.currentTime + lookAheadTime
        );
        // --- END OF FIX ---

        // 创建音频源
        const source = this.audioContext.createBufferSource();
        source.buffer = buffer;
        source.connect(this.gainNode);

        // 🎯 修复：将源添加到活跃源集合中
        this.activeSources.add(source);
        this.currentSource = source; // 保持向后兼容

        // 播放状态已在playChunk中设置，这里不需要重复设置
        // 但确保状态正确
        if (!this.isPlaying) {
          this.isPlaying = true;
          this.onStart?.();
        }

        // 设置结束回调
        source.onended = () => {
          console.debug(`🎵 Audio chunk completed: ${chunkId || "unnamed"}`);

          // 🎯 修复：从活跃源集合中移除已完成的源
          this.activeSources.delete(source);

          // 如果没有更多音频在队列中且没有活跃源，标记播放完成
          if (
            this.audioQueue.length === 0 &&
            !this.isProcessingQueue &&
            this.activeSources.size === 0
          ) {
            this.isPlaying = false;
            this.onStop?.();
          }
        };

        // 播放音频
        source.start(startTime);

        // 更新下一个播放时间
        this.nextScheduledTime = startTime + buffer.duration;

        console.debug(
          `🎵 Audio scheduled at ${startTime.toFixed(
            3
          )}s, duration: ${buffer.duration.toFixed(3)}s`
        );

        // 如果队列太满，稍微等待
        if (this.audioQueue.length > 5) {
          await new Promise((resolve) => setTimeout(resolve, 10));
        }
      }
    } catch (error) {
      console.error("❌ Error processing audio queue:", error);
      this.onError?.(error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * 🎯 新增：处理音频数据
   */
  async processAudioData(base64Data) {
    // 验证输入数据
    if (typeof base64Data !== "string" || base64Data.length === 0) {
      throw new Error("Invalid audio data: empty or non-string");
    }

    // 解码base64数据
    let binaryString;
    try {
      binaryString = atob(base64Data);
    } catch (error) {
      throw new Error(`Base64 decode failed: ${error.message}`);
    }

    if (binaryString.length === 0) {
      console.warn("⚠️ Empty audio data after base64 decode");
      return null;
    }

    if (binaryString.length % 2 !== 0) {
      console.warn(
        "⚠️ Audio data length is not even (PCM16 requires 2 bytes per sample), padding with zero"
      );
      binaryString += "\0";
    }

    const uint8Array = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      uint8Array[i] = binaryString.charCodeAt(i);
    }

    // 转换为Float32Array
    const int16Array = new Int16Array(uint8Array.buffer);
    const float32Array = new Float32Array(int16Array.length);

    // 使用更高效的转换方法
    let maxAmplitude = 0;
    for (let i = 0; i < int16Array.length; i++) {
      const normalized = int16Array[i] / 32768.0;
      float32Array[i] = normalized;
      maxAmplitude = Math.max(maxAmplitude, Math.abs(normalized));
    }

    // 检查音频是否太安静
    if (maxAmplitude < 0.001) {
      console.debug("🔇 Audio appears to be silent");
    }

    // 创建音频缓冲区
    const audioBuffer = this.audioContext.createBuffer(
      1,
      float32Array.length,
      this.sampleRate
    );
    audioBuffer.copyToChannel(float32Array, 0);

    return audioBuffer;
  }

  /**
   * 中断播放 - 修复版本：确保所有音频源都被停止
   */
  interrupt() {
    console.log("🛑 DirectAudioPlayer interrupted");
    this.interruptFlag = true;

    // --- START OF FIX ---
    // 1. 立即停止所有正在播放或已调度的音频源
    this.activeSources.forEach((source) => {
      try {
        source.stop();
      } catch (e) {
        // 忽略已经停止的源可能抛出的错误
        console.debug("Source already stopped or invalid state.", e);
      }
    });
    this.activeSources.clear();
    // --- END OF FIX ---

    // 2. 清空待播放队列
    this.audioQueue = [];
    this.processedChunks.clear();

    // 3. 重置播放状态和调度时间
    this.isProcessingQueue = false;
    this.nextScheduledTime = this.audioContext
      ? this.audioContext.currentTime
      : 0;

    if (this.isPlaying) {
      this.isPlaying = false;
      this.onStop?.();
    }

    // 4. 短暂延迟后重置中断标志，准备接收新音频
    setTimeout(() => {
      this.interruptFlag = false;
      console.log("🔄 DirectAudioPlayer ready for new audio");
    }, 100);
  }

  /**
   * 停止当前播放 - 修复版本：停止所有活跃源
   */
  _stopCurrent() {
    // 🎯 修复：停止所有活跃的音频源
    this.activeSources.forEach((source) => {
      try {
        source.stop();
      } catch (error) {
        // 忽略已经停止的源的错误
        console.debug("Source already stopped:", error);
      }
    });

    // 清空活跃源集合
    this.activeSources.clear();
    this.currentSource = null;

    if (this.isPlaying) {
      this.isPlaying = false;
      this.onStop?.();
    }
  }

  /**
   * 获取播放状态 - 修复版本：包含活跃源信息
   */
  getStatus() {
    return {
      isPlaying: this.isPlaying,
      isInterrupted: this.interruptFlag,
      queueLength: this.audioQueue.length,
      activeSourcesCount: this.activeSources.size, // 🎯 新增：活跃源数量
      sampleRate: this.sampleRate,
      audioContextState: this.audioContextState,
      errorCount: this.errorCount,
      isProcessingQueue: this.isProcessingQueue,
      nextScheduledTime: this.nextScheduledTime,
      processedChunksCount: this.processedChunks.size,
    };
  }
}

class AudioManager {
  constructor() {
    this.isRecording = false;
    this.isPlaying = false;
    this.mediaRecorder = null;
    this.audioStream = null;
    this.audioContext = null;
    this.analyser = null;
    this.dataArray = null;

    // 🎯 新增：直接音频播放器
    this.directPlayer = new DirectAudioPlayer(24000);
    this.audioPlaybackMode = "direct"; // "direct" 或 "legacy"

    // Recording settings
    this.sampleRate = 24000;
    this.channels = 1;
    this.bufferSize = 4096;

    // Audio processing
    this.processor = null;
    this.recordedChunks = [];

    // Event handlers
    this.onRecordingStart = null;
    this.onRecordingStop = null;
    this.onAudioData = null;
    this.onPlaybackStart = null;
    this.onPlaybackEnd = null;
    this.onError = null;
    this.onVolumeChange = null;

    // Audio visualization
    this.isVisualizing = false;
    this.animationId = null;

    // 移除冲突的AudioQueue相关属性
    // this.audioQueue = new AudioQueue(); // 已移除
    // this.currentAudio = null; // 已移除
  }

  /**
   * Initialize audio system
   */
  async initialize() {
    try {
      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Audio recording not supported in this browser");
      }

      // Initialize audio context
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)({
        sampleRate: this.sampleRate,
      });

      // Set global reference
      window.audioContext = this.audioContext;

      // 🎯 初始化直接音频播放器
      await this.directPlayer.initialize(this.audioContext);

      // 设置直接播放器回调
      this.directPlayer.onStart = () => {
        this.isPlaying = true;
        this.onPlaybackStart?.();
      };

      this.directPlayer.onStop = () => {
        this.isPlaying = false;
        this.onPlaybackEnd?.();
      };

      this.directPlayer.onError = (error) => {
        this.onError?.(error.message);
      };

      console.log("🎵 Audio system initialized with DirectAudioPlayer");
      return true;
    } catch (error) {
      console.error("Failed to initialize audio system:", error);
      this.onError?.(error.message);
      return false;
    }
  }

  /**
   * Request microphone permission and setup audio stream
   */
  async requestMicrophonePermission() {
    try {
      const constraints = {
        audio: {
          sampleRate: this.sampleRate,
          channelCount: this.channels,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
        video: false,
      };

      this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log("🎤 Microphone permission granted");

      // Setup audio analysis
      this.setupAudioAnalysis();

      return true;
    } catch (error) {
      console.error("Microphone permission denied:", error);
      this.onError?.("Microphone permission denied");
      return false;
    }
  }

  /**
   * Setup audio analysis for visualization and processing
   */
  setupAudioAnalysis() {
    if (!this.audioContext || !this.audioStream) return;

    try {
      const source = this.audioContext.createMediaStreamSource(
        this.audioStream
      );
      this.analyser = this.audioContext.createAnalyser();
      this.analyser.fftSize = 256;
      this.analyser.smoothingTimeConstant = 0.8;

      const bufferLength = this.analyser.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);

      source.connect(this.analyser);

      // Setup audio processing for real-time data
      this.processor = this.audioContext.createScriptProcessor(
        this.bufferSize,
        this.channels,
        this.channels
      );
      source.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      this.processor.onaudioprocess = (event) => {
        if (this.isRecording && this.onAudioData) {
          const inputBuffer = event.inputBuffer;
          const audioData = inputBuffer.getChannelData(0);

          // Convert to 16-bit PCM
          const pcmData = this.floatTo16BitPCM(audioData);
          this.onAudioData(pcmData.buffer);
        }
      };

      console.log("🎵 Audio analysis setup completed");
    } catch (error) {
      console.error("Failed to setup audio analysis:", error);
      this.onError?.("Failed to setup audio analysis: " + error.message);
    }
  }

  /**
   * Start recording audio
   */
  async startRecording() {
    try {
      if (!this.audioContext) {
        await this.initialize();
      }

      if (!this.audioStream) {
        const success = await this.requestMicrophonePermission();
        if (!success) return false;
      }

      // Resume audio context if suspended
      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
      }

      this.isRecording = true;
      this.recordedChunks = [];

      // Setup MediaRecorder for backup recording
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType: "audio/webm;codecs=opus",
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };

      this.mediaRecorder.start(100); // Record in 100ms chunks

      console.log("🎤 Recording started");
      this.onRecordingStart?.();

      return true;
    } catch (error) {
      console.error("Failed to start recording:", error);
      this.onError?.(error.message);
      return false;
    }
  }

  /**
   * Stop recording audio
   */
  async stopRecording() {
    try {
      this.isRecording = false;

      if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
        this.mediaRecorder.stop();
      }

      console.log("🛑 Recording stopped" + new Date().toISOString());
      this.onRecordingStop?.();

      return true;
    } catch (error) {
      console.error("Failed to stop recording:", error);
      this.onError?.(error.message);
      return false;
    }
  }

  /**
   * 统一的音频播放接口 - 支持直接通道和降级模式
   * @param {string} base64Data - Base64 encoded audio data
   * @param {string} format - Audio format (pcm16, etc.)
   */
  async playAudio(base64Data, format = "pcm16") {
    try {
      if (!this.audioContext) {
        await this.initialize();
      }

      // Resume audio context if suspended
      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
        console.log("🎵 Audio context resumed");
      }

      console.log("🔊 Playing audio:", {
        format: format,
        dataLength: base64Data ? base64Data.length : 0,
        mode: this.audioPlaybackMode,
      });

      if (format === "pcm16") {
        // 🎯 使用优化的直接播放器
        await this.directPlayer.playChunk(base64Data);
        console.log("🎵 Audio played using DirectAudioPlayer");
      } else {
        // 降级到传统解码播放
        await this._playEncodedAudio(base64Data);
        console.log("🎵 Audio played using legacy decoder");
      }
    } catch (error) {
      console.error("Failed to play audio:", error);
      this.onError?.(error.message);
    }
  }

  /**
   * 传统编码音频播放（向后兼容）
   * @param {string} base64Data - Base64 encoded audio data
   */
  async _playEncodedAudio(base64Data) {
    try {
      if (!this.audioContext) {
        await this.initialize();
      }

      // Resume audio context if suspended
      if (this.audioContext.state === "suspended") {
        await this.audioContext.resume();
        console.log("🎵 Audio context resumed");
      }

      console.log(
        "🔊 Playing encoded audio (legacy mode), data length:",
        base64Data.length
      );

      const binaryString = atob(base64Data);
      const uint8Array = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }

      const audioBuffer = await this.audioContext.decodeAudioData(
        uint8Array.buffer
      );

      console.log(
        "🔊 Decoded audio buffer, duration:",
        audioBuffer.duration,
        "seconds"
      );

      // Create gain node for volume control
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = 1.0;
      gainNode.connect(this.audioContext.destination);

      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(gainNode);

      this.isPlaying = true;
      this.onPlaybackStart?.();

      source.onended = () => {
        console.log("🔊 Legacy audio playback ended");
        this.isPlaying = false;
        this.onPlaybackEnd?.();
      };

      console.log("🔊 Starting legacy audio playback...");
      source.start(0);
    } catch (error) {
      console.error("❌ Failed to play encoded audio:", error);
      this.onError?.("音频播放失败: " + error.message);

      // Reset playing state
      this.isPlaying = false;
    }
  }

  /**
   * Stop current audio playback
   */
  stopAudio() {
    console.log("🛑 Stopping audio playback");

    // 使用直接播放器的中断方法
    if (this.directPlayer) {
      this.directPlayer.interrupt();
    }

    // 重置播放状态
    this.isPlaying = false;
    this.onPlaybackEnd?.();
  }

  /**
   * Handle speech interruption
   */
  handleSpeechInterruption() {
    console.log("🎤 Speech interruption detected, stopping audio playback");
    this.stopAudio();
  }

  /**
   * Start audio visualization
   * @param {function} visualizationCallback - Callback function for visualization data
   */
  startVisualization(visualizationCallback) {
    if (!this.analyser) return;

    this.isVisualizing = true;

    const draw = () => {
      if (!this.isVisualizing) return;

      this.analyser.getByteFrequencyData(this.dataArray);
      visualizationCallback(this.dataArray);

      this.animationId = requestAnimationFrame(draw);
    };

    draw();
  }

  /**
   * Stop audio visualization
   */
  stopVisualization() {
    this.isVisualizing = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  /**
   * Get current audio volume level
   * @returns {number} Volume level (0-100)
   */
  getCurrentVolume() {
    if (!this.analyser || !this.dataArray) {
      return 0;
    }

    try {
      this.analyser.getByteFrequencyData(this.dataArray);

      // 计算RMS音量
      let sum = 0;
      for (let i = 0; i < this.dataArray.length; i++) {
        sum += this.dataArray[i] * this.dataArray[i];
      }
      const rms = Math.sqrt(sum / this.dataArray.length);
      const volume = Math.round((rms / 255) * 100);

      return Math.min(100, Math.max(0, volume));
    } catch (error) {
      console.error("Failed to get current volume:", error);
      return 0;
    }
  }

  /**
   * Get time domain data for waveform visualization
   * @returns {Uint8Array} Time domain data
   */
  getTimeDomainData() {
    if (!this.analyser || !this.dataArray) {
      return new Uint8Array(128);
    }

    try {
      this.analyser.getByteTimeDomainData(this.dataArray);
      return this.dataArray;
    } catch (error) {
      console.error("Failed to get time domain data:", error);
      return new Uint8Array(128);
    }
  }

  /**
   * Convert Float32Array to 16-bit PCM
   * @param {Float32Array} floatSamples - Float audio samples
   * @returns {Int16Array} 16-bit PCM data
   */
  floatTo16BitPCM(floatSamples) {
    const int16Array = new Int16Array(floatSamples.length);
    for (let i = 0; i < floatSamples.length; i++) {
      const sample = Math.max(-1, Math.min(1, floatSamples[i]));
      int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
    }
    return int16Array;
  }

  /**
   * Clean up audio resources
   */
  cleanup() {
    this.stopRecording();
    this.stopAudio();
    this.stopVisualization();

    if (this.audioStream) {
      this.audioStream.getTracks().forEach((track) => track.stop());
      this.audioStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.analyser = null;
    this.processor = null;

    // 清理直接播放器
    if (this.directPlayer) {
      this.directPlayer.interrupt();
    }
  }
}

// Export for use in other modules
window.AudioManager = AudioManager;
window.DirectAudioPlayer = DirectAudioPlayer;
