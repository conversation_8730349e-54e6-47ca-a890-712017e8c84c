"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Bug, Download, Trash2, <PERSON><PERSON>, Eye, EyeOff, ChevronDown, ChevronUp } from "lucide-react"
import { useWebSocketManager } from "@/hooks/use-websocket-manager"
import { useSessionManager } from "@/hooks/use-session-manager"
import { useErrorHandler } from "@/hooks/use-error-handler"
import { usePerformanceMonitor } from "@/hooks/use-performance-monitor"
import { useChatStore } from "@/store/chat-store"
import { useAppStore } from "@/store/app-store"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

interface DebugLog {
  id: string
  timestamp: Date
  level: "info" | "warn" | "error" | "debug"
  category: "websocket" | "audio" | "session" | "performance" | "ui" | "general"
  message: string
  data?: any
}

export function DebugPanel() {
  const { currentSession } = useWebSocketManager()
  const { getSessionStats } = useSessionManager()
  const { getErrorStats } = useErrorHandler()
  const { getPerformanceStats } = usePerformanceMonitor()
  const { messages } = useChatStore()
  const { connectionStatus, conversationMode } = useAppStore()
  const t = useTranslations('debugPanel')

  const [isVisible, setIsVisible] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const [debugLogs, setDebugLogs] = useState<DebugLog[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [autoScroll, setAutoScroll] = useState(true)

  // Intercept console logs for debugging
  useEffect(() => {
    const originalLog = console.log
    const originalWarn = console.warn
    const originalError = console.error

    const addDebugLog = (level: DebugLog["level"], args: any[]) => {
      const message = args.map(arg => 
        typeof arg === "object" ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(" ")

      // Categorize based on message content
      let category: DebugLog["category"] = "general"
      if (message.includes("WebSocket") || message.includes("🔗") || message.includes("📥") || message.includes("📤")) {
        category = "websocket"
      } else if (message.includes("🎤") || message.includes("🔊") || message.includes("audio")) {
        category = "audio"
      } else if (message.includes("Session") || message.includes("🎯")) {
        category = "session"
      } else if (message.includes("Performance") || message.includes("🔍")) {
        category = "performance"
      }

      const log: DebugLog = {
        id: `log_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        timestamp: new Date(),
        level,
        category,
        message,
        data: args.length > 1 ? args.slice(1) : undefined
      }

      setDebugLogs(prev => {
        const newLogs = [log, ...prev].slice(0, 500) // Keep last 500 logs
        return newLogs
      })
    }

    console.log = (...args) => {
      originalLog(...args)
      addDebugLog("info", args)
    }

    console.warn = (...args) => {
      originalWarn(...args)
      addDebugLog("warn", args)
    }

    console.error = (...args) => {
      originalError(...args)
      addDebugLog("error", args)
    }

    return () => {
      console.log = originalLog
      console.warn = originalWarn
      console.error = originalError
    }
  }, [])

  const getLevelColor = (level: DebugLog["level"]) => {
    switch (level) {
      case "info": return "text-blue-500"
      case "warn": return "text-yellow-500"
      case "error": return "text-red-500"
      case "debug": return "text-purple-500"
      default: return "text-gray-500"
    }
  }

  const getCategoryColor = (category: DebugLog["category"]) => {
    switch (category) {
      case "websocket": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "audio": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "session": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
      case "performance": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
      case "ui": return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200"
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const filteredLogs = debugLogs.filter(log => 
    selectedCategory === "all" || log.category === selectedCategory
  )

  const exportDebugData = () => {
    const debugData = {
      timestamp: new Date().toISOString(),
      session: currentSession,
      sessionStats: getSessionStats(),
      errorStats: getErrorStats(),
      performanceStats: getPerformanceStats(),
      messages: messages.slice(-20), // Last 20 messages
      connectionStatus,
      conversationMode,
      logs: debugLogs.slice(0, 100) // Last 100 logs
    }

    const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `audio-agent-debug-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success(t('debugDataExported'))
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(t('copiedToClipboard'))
    }).catch(() => {
      toast.error(t('failedToCopy'))
    })
  }

  const clearLogs = () => {
    setDebugLogs([])
    toast.success(t('debugLogsCleared'))
  }

  if (!isVisible) {
    return (
      <Button
        size="sm"
        variant="outline"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
      >
        <Bug className="w-4 h-4 mr-2" />
        {t('debug')}
      </Button>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 right-4 z-50 w-96 max-h-96 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Bug className="w-4 h-4 text-purple-600" />
          <span className="text-sm font-medium">{t('title')}</span>
          <span className="text-xs px-2 py-1 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300">
            {filteredLogs.length}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          <Button size="sm" variant="ghost" onClick={exportDebugData} className="h-6 px-2">
            <Download className="w-3 h-3" />
          </Button>
          <Button size="sm" variant="ghost" onClick={clearLogs} className="h-6 px-2">
            <Trash2 className="w-3 h-3" />
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={() => setIsExpanded(!isExpanded)} 
            className="h-6 px-2"
          >
            {isExpanded ? <ChevronDown className="w-3 h-3" /> : <ChevronUp className="w-3 h-3" />}
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            onClick={() => setIsVisible(false)} 
            className="h-6 px-2"
          >
            <EyeOff className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: "auto" }}
            exit={{ height: 0 }}
            className="overflow-hidden"
          >
            {/* Category Filter */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full text-xs p-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              >
                <option value="all">{t('allCategories')}</option>
                <option value="websocket">{t('websocket')}</option>
                <option value="audio">{t('audio')}</option>
                <option value="session">{t('session')}</option>
                <option value="performance">{t('performance')}</option>
                <option value="ui">{t('ui')}</option>
                <option value="general">{t('general')}</option>
              </select>
            </div>

            {/* Logs */}
            <div className="max-h-64 overflow-y-auto p-2 space-y-1">
              {filteredLogs.map((log) => (
                <motion.div
                  key={log.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="text-xs p-2 rounded bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                  onClick={() => copyToClipboard(log.message)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <span className={`font-medium ${getLevelColor(log.level)}`}>
                        {log.level.toUpperCase()}
                      </span>
                      <span className={`px-1 py-0.5 rounded text-xs ${getCategoryColor(log.category)}`}>
                        {log.category}
                      </span>
                    </div>
                    <span className="text-gray-500 text-xs">
                      {log.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-gray-700 dark:text-gray-300 break-words">
                    {log.message}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
