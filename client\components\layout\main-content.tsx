"use client"

import { motion } from "framer-motion"
import { ControlPanel } from "@/components/conversation/control-panel"
import { ChatInterface } from "@/components/chat/chat-interface"

export function MainContent() {
  return (
    <motion.main
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
      className="flex-1 p-4 overflow-hidden"
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
        <div className="flex flex-col">
          <ControlPanel />
        </div>

        <div className="flex flex-col min-h-0">
          <ChatInterface />
        </div>
      </div>
    </motion.main>
  )
}
