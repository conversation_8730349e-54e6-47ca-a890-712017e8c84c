"use client"

import { useCallback, useRef, useState } from "react"
import { useChatStore } from "@/store/chat-store"

interface TranscriptBuffer {
  id: string
  source: "paraformer" | "qwen"
  text: string
  quality: number
  timestamp: number
  timeout?: NodeJS.Timeout
}

interface TranscriptCoordination {
  paraformerBuffer: TranscriptBuffer | null
  qwenBuffer: TranscriptBuffer | null
  waitingForBoth: boolean
  coordinationTimeout?: NodeJS.Timeout
}

/**
 * Sophisticated transcription buffer system matching the old frontend
 * Handles dual-source transcription with intelligent coordination
 */
export const useTranscriptionBuffer = () => {
  const { addMessage, replaceStreamingMessage, completeStreamingMessage } = useChatStore()
  const coordinationRef = useRef<TranscriptCoordination>({
    paraformerBuffer: null,
    qwenBuffer: null,
    waitingForBoth: false
  })

  const [transcriptStatus, setTranscriptStatus] = useState("Ready for speech...")
  const userStreamingIdRef = useRef<string | null>(null)
  const userStreamingTextRef = useRef<string>("")

  const generateBufferId = useCallback(() => {
    return `buffer_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }, [])

  const clearCoordinationTimeout = useCallback(() => {
    if (coordinationRef.current.coordinationTimeout) {
      clearTimeout(coordinationRef.current.coordinationTimeout)
      coordinationRef.current.coordinationTimeout = undefined
    }
  }, [])

  const clearBufferTimeout = useCallback((buffer: TranscriptBuffer) => {
    if (buffer.timeout) {
      clearTimeout(buffer.timeout)
    }
  }, [])

  const selectBestTranscript = useCallback((paraformer: TranscriptBuffer | null, qwen: TranscriptBuffer | null): TranscriptBuffer | null => {
    if (!paraformer && !qwen) return null
    if (!paraformer) return qwen
    if (!qwen) return paraformer

    // Intelligent selection logic based on quality and length
    const paraformerScore = paraformer.quality * (1 + paraformer.text.length * 0.01)
    const qwenScore = qwen.quality * (1 + qwen.text.length * 0.01)

    console.log("🧠 Transcript selection:", {
      paraformer: { text: paraformer.text, quality: paraformer.quality, score: paraformerScore },
      qwen: { text: qwen.text, quality: qwen.quality, score: qwenScore }
    })

    return paraformerScore >= qwenScore ? paraformer : qwen
  }, [])

  const processCoordinatedTranscript = useCallback(() => {
    const { paraformerBuffer, qwenBuffer } = coordinationRef.current
    
    clearCoordinationTimeout()
    
    const bestTranscript = selectBestTranscript(paraformerBuffer, qwenBuffer)
    
    if (bestTranscript) {
      console.log("✅ Processing coordinated transcript:", bestTranscript.text, "from:", bestTranscript.source)
      
      addMessage({
        role: "user",
        content: bestTranscript.text,
        source: bestTranscript.source,
        quality: bestTranscript.quality,
        messageKey: bestTranscript.id
      })
      
      setTranscriptStatus("Transcript processed")
    }

    // Clear buffers
    if (paraformerBuffer) clearBufferTimeout(paraformerBuffer)
    if (qwenBuffer) clearBufferTimeout(qwenBuffer)
    
    coordinationRef.current = {
      paraformerBuffer: null,
      qwenBuffer: null,
      waitingForBoth: false
    }

    // Reset status after a short delay
    setTimeout(() => {
      setTranscriptStatus("Ready for speech...")
    }, 1000)
  }, [addMessage, selectBestTranscript, clearCoordinationTimeout, clearBufferTimeout])

  const handleTranscriptComplete = useCallback((
    source: "paraformer" | "qwen",
    text: string,
    quality: number = 0.8,
    messageKey?: string
  ) => {
    if (!text || text.trim().length === 0) {
      console.log("⚠️ Empty transcript received from:", source)
      return
    }

    // Complete user streaming message if active
    if (userStreamingIdRef.current) {
      console.log("🎯 Completing user streaming message:", userStreamingIdRef.current)
      // Update with final text
      replaceStreamingMessage(userStreamingIdRef.current, text.trim())
      completeStreamingMessage(userStreamingIdRef.current)
      userStreamingIdRef.current = null
      userStreamingTextRef.current = ""
      console.log("🎯 User streaming message completed")
    }

    const buffer: TranscriptBuffer = {
      id: messageKey || generateBufferId(),
      source,
      text: text.trim(),
      quality,
      timestamp: Date.now()
    }

    console.log("📝 Transcript buffer received:", source, text, "quality:", quality)

    // Update coordination state
    if (source === "paraformer") {
      coordinationRef.current.paraformerBuffer = buffer
    } else {
      coordinationRef.current.qwenBuffer = buffer
    }

    const { paraformerBuffer, qwenBuffer } = coordinationRef.current

    // Check if we should wait for the other source
    const shouldWaitForBoth = !coordinationRef.current.waitingForBoth && 
                             (paraformerBuffer || qwenBuffer) && 
                             !(paraformerBuffer && qwenBuffer)

    if (shouldWaitForBoth) {
      coordinationRef.current.waitingForBoth = true
      setTranscriptStatus(`Waiting for ${source === "paraformer" ? "Qwen" : "Paraformer"}...`)
      
      // Set coordination timeout (1.5 seconds like the old frontend)
      coordinationRef.current.coordinationTimeout = setTimeout(() => {
        console.log("⏰ Coordination timeout reached, processing available transcript")
        processCoordinatedTranscript()
      }, 1500)

      // Set individual buffer timeout
      buffer.timeout = setTimeout(() => {
        console.log("⏰ Buffer timeout for:", source)
        if (coordinationRef.current.waitingForBoth) {
          processCoordinatedTranscript()
        }
      }, 2000)

    } else if (paraformerBuffer && qwenBuffer) {
      // Both transcripts available, process immediately
      console.log("🎯 Both transcripts available, processing immediately")
      processCoordinatedTranscript()
    } else {
      // Single transcript, process immediately
      console.log("🎯 Single transcript, processing immediately")
      processCoordinatedTranscript()
    }
  }, [generateBufferId, processCoordinatedTranscript, replaceStreamingMessage, completeStreamingMessage])

  const handleTranscriptDelta = useCallback((
    source: "paraformer" | "qwen",
    text: string
  ) => {
    if (text && text.trim().length > 0) {
      setTranscriptStatus(`${source}: ${text.trim()}`)
      console.log("📝 Transcript delta:", source, text)

      // Handle user transcript streaming
      if (!userStreamingIdRef.current) {
        // Start new streaming user message
        console.log("🎯 Starting new user streaming message")
        userStreamingTextRef.current = text
        const streamingId = addMessage({
          role: "user",
          content: text,
          isStreaming: true,
          messageKey: `user_${source}_${Date.now()}`
        })
        userStreamingIdRef.current = streamingId
        console.log("🎯 Created new user streaming message with ID:", streamingId)
      } else {
        // Continue streaming user message - accumulate text
        console.log("🎯 Continuing user streaming message:", userStreamingIdRef.current)
        userStreamingTextRef.current += text
        replaceStreamingMessage(userStreamingIdRef.current, userStreamingTextRef.current)
        console.log("🎯 Updated user streaming content, total length:", userStreamingTextRef.current.length)
      }
    }
  }, [addMessage, replaceStreamingMessage])

  const clearAllBuffers = useCallback(() => {
    const { paraformerBuffer, qwenBuffer } = coordinationRef.current
    
    clearCoordinationTimeout()
    
    if (paraformerBuffer) clearBufferTimeout(paraformerBuffer)
    if (qwenBuffer) clearBufferTimeout(qwenBuffer)
    
    coordinationRef.current = {
      paraformerBuffer: null,
      qwenBuffer: null,
      waitingForBoth: false
    }
    
    setTranscriptStatus("Ready for speech...")
    console.log("🧹 All transcript buffers cleared")
  }, [clearCoordinationTimeout, clearBufferTimeout])

  const getBufferStatus = useCallback(() => {
    const { paraformerBuffer, qwenBuffer, waitingForBoth } = coordinationRef.current
    
    return {
      hasParaformer: !!paraformerBuffer,
      hasQwen: !!qwenBuffer,
      waitingForBoth,
      paraformerText: paraformerBuffer?.text || "",
      qwenText: qwenBuffer?.text || "",
      paraformerQuality: paraformerBuffer?.quality || 0,
      qwenQuality: qwenBuffer?.quality || 0
    }
  }, [])

  return {
    handleTranscriptComplete,
    handleTranscriptDelta,
    clearAllBuffers,
    getBufferStatus,
    transcriptStatus
  }
}
