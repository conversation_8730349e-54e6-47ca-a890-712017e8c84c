"use client"

import { motion } from "framer-motion"
import { Wifi, WifiOff, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react"
import { useAppStore } from "@/store/app-store"
import { useSessionManager } from "@/hooks/use-session-manager"
import { useTranslations } from 'next-intl'

interface ConnectionStatusIndicatorProps {
  className?: string
  showText?: boolean
  size?: "sm" | "md" | "lg"
}

/**
 * Connection status indicator matching the old frontend's sophisticated status display
 */
export function ConnectionStatusIndicator({
  className = "",
  showText = true,
  size = "md"
}: ConnectionStatusIndicatorProps) {
  const { connectionStatus } = useAppStore()
  const { currentSession } = useSessionManager()
  const t = useTranslations('connectionStatus')

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case "connected":
        return {
          icon: CheckCircle,
          color: "text-green-600 dark:text-green-400",
          bgColor: "bg-green-100 dark:bg-green-900/20",
          text: t('connected'),
          description: t('connectedDescription'),
          pulse: false
        }
      case "connecting":
        return {
          icon: Wifi,
          color: "text-yellow-600 dark:text-yellow-400",
          bgColor: "bg-yellow-100 dark:bg-yellow-900/20",
          text: t('connecting'),
          description: t('connectingDescription'),
          pulse: true
        }
      case "disconnected":
        return {
          icon: WifiOff,
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-100 dark:bg-red-900/20",
          text: t('disconnected'),
          description: t('disconnectedDescription'),
          pulse: false
        }
      case "error":
        return {
          icon: AlertTriangle,
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-100 dark:bg-red-900/20",
          text: t('connectionError'),
          description: t('errorDescription'),
          pulse: false
        }
      default:
        return {
          icon: WifiOff,
          color: "text-gray-600 dark:text-gray-400",
          bgColor: "bg-gray-100 dark:bg-gray-900/20",
          text: t('unknown'),
          description: t('unknownDescription'),
          pulse: false
        }
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return {
          container: "px-2 py-1",
          icon: "w-3 h-3",
          text: "text-xs",
          dot: "w-2 h-2"
        }
      case "lg":
        return {
          container: "px-4 py-3",
          icon: "w-6 h-6",
          text: "text-base",
          dot: "w-4 h-4"
        }
      default: // md
        return {
          container: "px-3 py-2",
          icon: "w-4 h-4",
          text: "text-sm",
          dot: "w-3 h-3"
        }
    }
  }

  const status = getStatusConfig()
  const sizeClasses = getSizeClasses()
  const Icon = status.icon

  return (
    <div className={`inline-flex items-center space-x-2 rounded-full ${status.bgColor} ${sizeClasses.container} ${className}`}>
      {/* Status icon with optional pulse animation */}
      <motion.div
        className={status.color}
        animate={status.pulse ? { scale: [1, 1.1, 1] } : {}}
        transition={status.pulse ? { duration: 1.5, repeat: Infinity } : {}}
      >
        <Icon className={sizeClasses.icon} />
      </motion.div>

      {/* Status text */}
      {showText && (
        <span className={`font-medium ${status.color} ${sizeClasses.text}`}>
          {status.text}
        </span>
      )}

      {/* Connection quality indicator dot */}
      {connectionStatus === "connected" && (
        <motion.div
          className={`${sizeClasses.dot} bg-green-500 rounded-full`}
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
          title={t('connectionActive')}
        />
      )}

      {/* Session indicator */}
      {currentSession && connectionStatus === "connected" && (
        <div className="flex items-center space-x-1">
          <div className={`${sizeClasses.dot} bg-blue-500 rounded-full`} title={t('sessionActive')} />
          {size !== "sm" && (
            <span className={`${sizeClasses.text} text-muted-foreground`}>
              {t('session')}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * Detailed connection status component with additional information
 */
export function ConnectionStatusDetailed({ className = "" }: { className?: string }) {
  const { connectionStatus } = useAppStore()
  const { currentSession, getSessionStats } = useSessionManager()
  const t = useTranslations('connectionStatus')
  const sessionStats = getSessionStats()

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case "connected":
        return {
          icon: CheckCircle,
          color: "text-green-600 dark:text-green-400",
          bgColor: "bg-green-50 dark:bg-green-950/20",
          borderColor: "border-green-200 dark:border-green-800",
          text: t('connected'),
          description: t('connectedDetailedDescription')
        }
      case "connecting":
        return {
          icon: Wifi,
          color: "text-yellow-600 dark:text-yellow-400",
          bgColor: "bg-yellow-50 dark:bg-yellow-950/20",
          borderColor: "border-yellow-200 dark:border-yellow-800",
          text: t('connecting'),
          description: t('connectingDetailedDescription')
        }
      case "disconnected":
        return {
          icon: WifiOff,
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-50 dark:bg-red-950/20",
          borderColor: "border-red-200 dark:border-red-800",
          text: t('disconnected'),
          description: t('disconnectedDetailedDescription')
        }
      case "error":
        return {
          icon: AlertTriangle,
          color: "text-red-600 dark:text-red-400",
          bgColor: "bg-red-50 dark:bg-red-950/20",
          borderColor: "border-red-200 dark:border-red-800",
          text: t('connectionError'),
          description: t('errorDetailedDescription')
        }
      default:
        return {
          icon: WifiOff,
          color: "text-gray-600 dark:text-gray-400",
          bgColor: "bg-gray-50 dark:bg-gray-950/20",
          borderColor: "border-gray-200 dark:border-gray-800",
          text: t('unknown'),
          description: t('unknownDescription')
        }
    }
  }

  const status = getStatusConfig()
  const Icon = status.icon

  return (
    <div className={`p-4 rounded-lg border ${status.bgColor} ${status.borderColor} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className={status.color}>
          <Icon className="w-5 h-5" />
        </div>
        
        <div className="flex-1 space-y-2">
          <div className="flex items-center justify-between">
            <h4 className={`font-medium ${status.color}`}>
              {status.text}
            </h4>
            {connectionStatus === "connected" && (
              <motion.div
                className="w-2 h-2 bg-green-500 rounded-full"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            )}
          </div>
          
          <p className="text-sm text-muted-foreground">
            {status.description}
          </p>


        </div>
      </div>
    </div>
  )
}
