@tailwind base;
@tailwind components;
@tailwind utilities;

@custom-variant dark (&:is(.dark *));

@layer base {
  :root {
    --background: 220 20% 96%;
    --foreground: 220 10% 20%;
    --card: 220 20% 96%;
    --card-foreground: 220 10% 20%;
    --popover: 220 20% 96%;
    --popover-foreground: 220 10% 20%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 220 15% 90%;
    --secondary-foreground: 220 10% 20%;
    --muted: 220 15% 90%;
    --muted-foreground: 220 10% 40%;
    --accent: 220 15% 90%;
    --accent-foreground: 220 10% 20%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 15% 85%;
    --input: 220 15% 85%;
    --ring: 221 83% 53%;
    --radius: 1.25rem;
  }

  .dark {
    --background: 224 71% 12%;
    --foreground: 210 40% 98%;
    --card: 224 71% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 224 71% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217 33% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 20%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 33% 20%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 25%;
    --input: 217 33% 25%;
    --ring: 217 91% 60%;
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
    min-height: 100vh;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .glass-card {
    @apply bg-background/50 dark:bg-background/30 backdrop-blur-2xl border border-white/20 dark:border-white/10 rounded-3xl shadow-lg shadow-black/5;
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    will-change: transform;
  }

  .dark .glass-card {
    background: rgba(0, 0, 0, 0.2);
  }

  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.5), rgba(147, 51, 234, 0.5));
  }

  .clay-card {
    @apply bg-gray-100 dark:bg-gray-900 rounded-2xl;
    box-shadow: 12px 12px 24px rgba(163, 177, 198, 0.4), -12px -12px 24px rgba(255, 255, 255, 0.9);
    transition: box-shadow 0.2s ease, transform 0.15s ease;
    will-change: transform, box-shadow;
  }

  .dark .clay-card {
    box-shadow: 12px 12px 24px rgba(16, 24, 40, 0.6), -12px -12px 24px rgba(55, 65, 81, 0.4);
  }

  .clay-button {
    @apply bg-background rounded-2xl relative;
    box-shadow: 8px 8px 16px rgba(163, 177, 198, 0.6), -8px -8px 16px rgba(255, 255, 255, 0.8);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
  }

  .dark .clay-button {
    box-shadow: 8px 8px 16px rgba(16, 24, 40, 0.8), -8px -8px 16px rgba(55, 65, 81, 0.6);
  }

  .clay-button:hover {
    transform: translateY(-1px);
    box-shadow: 12px 12px 20px rgba(163, 177, 198, 0.7), -12px -12px 20px rgba(255, 255, 255, 0.9);
  }

  .dark .clay-button:hover {
    transform: translateY(-1px);
    box-shadow: 12px 12px 20px rgba(16, 24, 40, 0.9), -12px -12px 20px rgba(55, 65, 81, 0.7);
  }

  .clay-button:active {
    transform: translateY(1px);
    box-shadow: inset 6px 6px 12px rgba(163, 177, 198, 0.8), inset -6px -6px 12px rgba(255, 255, 255, 0.8);
  }

  .dark .clay-button:active {
    transform: translateY(1px);
    box-shadow: inset 6px 6px 12px rgba(16, 24, 40, 0.9), inset -6px -6px 12px rgba(55, 65, 81, 0.7);
  }

  /* Performance optimizations */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* GPU acceleration for better performance */
.clay-button,
.clay-card,
.glass-card {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
