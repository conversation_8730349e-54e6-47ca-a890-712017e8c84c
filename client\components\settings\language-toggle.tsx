"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Globe, ChevronDown } from "lucide-react"
import { useLocale } from 'next-intl'
import { useRouter, usePathname } from '@/i18n/navigation'
import { useTranslations } from 'next-intl'

type Language = "en" | "zh" | "ja"

const languages = {
  en: { name: "English", flag: "🇺🇸" },
  zh: { name: "中文", flag: "🇨🇳" },
  ja: { name: "日本語", flag: "🇯🇵" },
}

export function LanguageToggle() {
  const [isOpen, setIsOpen] = useState(false)
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()
  const t = useTranslations('language')

  const toggleOpen = () => setIsOpen(!isOpen)

  const switchLanguage = (newLocale: Language) => {
    // The new navigation API handles locale switching automatically
    router.replace(pathname, {locale: newLocale})
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <motion.button
        onClick={toggleOpen}
        className="flex items-center justify-center gap-3 px-4 py-2 clay-button min-w-[120px] relative overflow-hidden"
        aria-label={t('selectLanguage')}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        {/* Background gradient */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-950/30 dark:to-purple-950/30"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />

        <div className="flex items-center gap-2 relative z-10">
          <motion.div animate={{ rotate: isOpen ? 180 : 0 }} transition={{ duration: 0.3, ease: "easeInOut" }}>
            <Globe className="w-4 h-4 text-foreground" />
          </motion.div>

          <span className="font-medium text-foreground text-sm">{languages[locale as Language].name}</span>

          <motion.div animate={{ rotate: isOpen ? 180 : 0 }} transition={{ duration: 0.3, ease: "easeInOut" }}>
            <ChevronDown className="w-3 h-3 text-muted-foreground" />
          </motion.div>
        </div>

        {/* Ripple effect */}
        <motion.div
          className="absolute inset-0 bg-primary/10"
          initial={{ scale: 0, opacity: 0 }}
          whileTap={{ scale: 1.5, opacity: [0, 0.3, 0] }}
          transition={{ duration: 0.4 }}
        />
      </motion.button>

      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 z-40"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />

            {/* Dropdown */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{
                duration: 0.2,
                ease: "easeOut",
                type: "spring",
                stiffness: 400,
                damping: 25,
              }}
              className="absolute right-0 top-full mt-2 w-48 p-2 glass-card z-50 border border-white/20 dark:border-gray-800/20"
            >
              {Object.entries(languages).map(([code, { name, flag }], index) => (
                <motion.button
                  key={code}
                  onClick={() => switchLanguage(code as Language)}
                  className="w-full text-left px-3 py-3 rounded-lg hover:bg-secondary/50 transition-colors flex items-center gap-3 relative overflow-hidden"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.2 }}
                  whileHover={{
                    scale: 1.02,
                    backgroundColor: "rgba(var(--secondary), 0.8)",
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-lg">{flag}</span>
                  <span className="font-medium">{name}</span>

                  {locale === code && (
                    <motion.div
                      className="absolute right-3 w-2 h-2 bg-primary rounded-full"
                      layoutId="activeLanguage"
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                    />
                  )}

                  {/* Hover effect */}
                  <motion.div
                    className="absolute inset-0 bg-primary/5"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </motion.button>
              ))}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
