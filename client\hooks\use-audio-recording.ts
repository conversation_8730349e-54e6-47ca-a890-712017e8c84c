"use client"

import { useCallback, useRef, useState } from "react"
import { useAudioManager } from "./use-audio-manager"
import { useWebSocketManager } from "./use-websocket-manager"
import { useError<PERSON>and<PERSON> } from "./use-error-handler"
import { useAudioWorklet } from "./use-audio-worklet"
import { useAppStore } from "@/store/app-store"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

/**
 * Comprehensive audio recording hook that integrates with the WebSocket manager
 * for real-time audio streaming to the backend
 */
export const useAudioRecording = () => {
  const { startRecording, stopRecording, requestMicrophonePermission } = useAudioManager()
  const { sendAudioData, isConnected } = useWebSocketManager()
  const { handleAudioError, resolveError } = useErrorHandler()
  const {
    initializeWorklet,
    isInitialized: isWorkletInitialized,
    connectToAudioStream,
    disconnectFromAudioStream,
    setGainControl,
    setNoiseGate,
    toggleProcessing,
    audioStats
  } = useAudioWorklet()
  const { isConversationActive, setAudioVolume, toggleConversation } = useAppStore()
  const t = useTranslations('toast')

  const [isRecording, setIsRecording] = useState(false)
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const audioChunkCountRef = useRef(0)

  const startConversationRecording = useCallback(async () => {
    try {
      if (!isConnected) {
        toast.error("Please connect to the server first")
        return false
      }

      if (isRecording) {
        console.log("Already recording")
        return true
      }

      console.log("🎤 Starting conversation recording...")

      // Start recording with audio data callback
      const success = await startRecording((audioData: ArrayBuffer) => {
        if (isConnected && audioData.byteLength > 0) {
          // Send audio data to backend
          sendAudioData(audioData)
          audioChunkCountRef.current++

          // Log every 100 chunks to avoid spam
          if (audioChunkCountRef.current % 100 === 0) {
            console.log(`📡 Sent ${audioChunkCountRef.current} audio chunks`)
          }
        }
      })

      if (success) {
        setIsRecording(true)
        // Set conversation as active when recording starts
        if (!isConversationActive) {
          toggleConversation()
        }
        audioChunkCountRef.current = 0
        toast.success("Recording started - speak now!")
        return true
      } else {
        toast.error("Failed to start recording")
        return false
      }

    } catch (error) {
      console.error("Failed to start conversation recording:", error)
      handleAudioError("Failed to start recording", {
        error,
        isConnected,
        hasPermission: true // We assume permission was granted if we got this far
      })
      return false
    }
  }, [startRecording, sendAudioData, isConnected, isRecording])

  const stopConversationRecording = useCallback(async () => {
    try {
      if (!isRecording) {
        console.log("Not currently recording")
        return
      }

      console.log("🛑 Stopping conversation recording...")
      
      await stopRecording()
      setIsRecording(false)

      // Set conversation as inactive when recording stops
      if (isConversationActive) {
        toggleConversation()
      }

      console.log(`📊 Recording session complete. Sent ${audioChunkCountRef.current} audio chunks`)
      toast.success(t('recordingStopped'))

    } catch (error) {
      console.error("Failed to stop conversation recording:", error)
      handleAudioError("Failed to stop recording", {
        error,
        wasRecording: isRecording,
        chunkCount: audioChunkCountRef.current
      })
    }
  }, [stopRecording, isRecording])

  const toggleRecording = useCallback(async () => {
    if (isRecording) {
      await stopConversationRecording()
    } else {
      await startConversationRecording()
    }
  }, [isRecording, startConversationRecording, stopConversationRecording])

  // Manual conversation state management - no automatic triggers
  const handleConversationStateChange = useCallback(async () => {
    // This function is kept for compatibility but doesn't auto-manage recording
    console.log("🔄 Conversation state changed:", { isConversationActive, isRecording })
  }, [isConversationActive, isRecording])

  return {
    isRecording,
    startRecording: startConversationRecording,
    stopRecording: stopConversationRecording,
    toggleRecording,
    requestMicrophonePermission,
    handleConversationStateChange,
    audioChunkCount: audioChunkCountRef.current
  }
}
