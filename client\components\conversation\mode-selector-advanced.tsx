"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Bo<PERSON>, Target, Check } from "lucide-react"
import { useAppStore } from "@/store/app-store"
import { useWebSocketManager } from "@/hooks/use-websocket-manager"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

interface ModeOption {
  id: "standard" | "interviewer"
  icon: React.ReactNode
  title: string
  description: string
  color: string
  bgColor: string
}

// This will be moved inside the component to use translations

interface ModeSelectorAdvancedProps {
  className?: string
  onModeSelect?: (mode: "standard" | "interviewer") => void
}

/**
 * Advanced mode selector component matching the old frontend's sophisticated design
 */
export function ModeSelectorAdvanced({ className = "", onModeSelect }: ModeSelectorAdvancedProps) {
  const { conversationMode, setConversationMode, connectionStatus } = useAppStore()
  const { sendModeSelection } = useWebSocketManager()
  const [isSelecting, setIsSelecting] = useState(false)
  const t = useTranslations('modeSelector')

  const modeOptions: ModeOption[] = [
    {
      id: "standard",
      icon: <Bot className="w-6 h-6" />,
      title: t('standardTitle'),
      description: t('standardDescription'),
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-50 dark:bg-blue-950/20 hover:bg-blue-100 dark:hover:bg-blue-950/30"
    },
    {
      id: "interviewer",
      icon: <Target className="w-6 h-6" />,
      title: t('interviewerTitle'),
      description: t('interviewerDescription'),
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-50 dark:bg-purple-950/20 hover:bg-purple-100 dark:hover:bg-purple-950/30"
    }
  ]

  const handleModeSelect = async (mode: "standard" | "interviewer") => {
    if (isSelecting || mode === conversationMode) return

    setIsSelecting(true)

    try {
      // Update local state immediately for responsive UI
      setConversationMode(mode)
      
      // Send mode selection to backend if connected
      if (connectionStatus === "connected") {
        const success = await sendModeSelection(mode)
        if (success) {
          const modeTitle = mode === "standard" ? t('standardTitle') : t('interviewerTitle')
          toast.success(t('modeSwitchedTo', { mode: modeTitle }))
        } else {
          toast.error(t('failedToUpdateMode'))
          // Revert local state on failure
          setConversationMode(conversationMode)
        }
      } else {
        const modeTitle = mode === "standard" ? t('standardTitle') : t('interviewerTitle')
        toast.success(t('modeSetTo', { mode: modeTitle }))
        toast.info(t('modeWillBeApplied'))
      }

      // Call callback if provided
      onModeSelect?.(mode)

    } catch (error) {
      console.error("Failed to select mode:", error)
      toast.error(t('failedToSwitchMode'))
      // Revert local state on error
      setConversationMode(conversationMode)
    } finally {
      setIsSelecting(false)
    }
  }

  // Component disabled - return null to not render anything
  return null
}
