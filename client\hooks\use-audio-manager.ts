"use client"

import { useEffect, useRef, useCallback } from "react"
import { useAppStore } from "@/store/app-store"
import { toast } from "sonner"

// Audio configuration matching the backend
const AUDIO_CONFIG = {
  sampleRate: 24000,
  channels: 1,
  format: "pcm16",
  chunkSize: 1024,
  bufferSize: 4096
}

export const useAudioManager = () => {
  const { isConversationActive, setAudioVolume, setHasAudioPermission, hasAudioPermission } = useAppStore()

  const audioContextRef = useRef<AudioContext | null>(null)
  const analyserRef = useRef<AnalyserNode | null>(null)
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const animationFrameRef = useRef<number | null>(null)

  // Recording-specific refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const isRecordingRef = useRef(false)
  const audioChunksRef = useRef<Blob[]>([])
  const processorRef = useRef<ScriptProcessorNode | null>(null)
  const workletNodeRef = useRef<AudioWorkletNode | null>(null)
  const onAudioDataRef = useRef<((data: ArrayBuffer) => void) | null>(null)

  const requestMicrophonePermission = useCallback(async () => {
    if (typeof window === 'undefined' || !navigator?.mediaDevices) {
      console.log('Media devices not available or running on server')
      return null
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: AUDIO_CONFIG.sampleRate,
          channelCount: AUDIO_CONFIG.channels,
        },
      })
      streamRef.current = stream
      setHasAudioPermission(true)
      toast.success("Microphone access granted!")
      return stream
    } catch (error) {
      console.error("Microphone permission denied:", error)
      setHasAudioPermission(false)
      toast.error("Microphone access denied. Please enable it in your browser settings.")
      return null
    }
  }, [setHasAudioPermission])

  const initializeAudioContext = useCallback(async () => {
    if (typeof window === 'undefined') {
      console.log('AudioContext not available on server')
      return null
    }

    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: AUDIO_CONFIG.sampleRate,
      })
    }

    if (audioContextRef.current.state === "suspended") {
      await audioContextRef.current.resume()
    }

    return audioContextRef.current
  }, [])

  const startRecording = useCallback(async (onAudioData?: (data: ArrayBuffer) => void) => {
    try {
      if (isRecordingRef.current) {
        console.log("Already recording")
        return false
      }

      const stream = streamRef.current || await requestMicrophonePermission()
      if (!stream) {
        throw new Error("No audio stream available")
      }

      const audioContext = await initializeAudioContext()
      onAudioDataRef.current = onAudioData || null

      // Create audio processing chain
      const source = audioContext.createMediaStreamSource(stream)
      sourceRef.current = source

      // Create script processor for real-time audio data
      const processor = audioContext.createScriptProcessor(AUDIO_CONFIG.bufferSize, 1, 1)
      processorRef.current = processor

      processor.onaudioprocess = (event) => {
        if (!isRecordingRef.current) return

        const inputBuffer = event.inputBuffer
        const inputData = inputBuffer.getChannelData(0)

        // Convert float32 to int16 (PCM16)
        const pcm16Data = new Int16Array(inputData.length)
        for (let i = 0; i < inputData.length; i++) {
          const sample = Math.max(-1, Math.min(1, inputData[i]))
          pcm16Data[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF
        }

        // Send audio data if callback is provided
        if (onAudioDataRef.current) {
          onAudioDataRef.current(pcm16Data.buffer)
        }
      }

      // Connect audio processing chain
      source.connect(processor)
      processor.connect(audioContext.destination)

      isRecordingRef.current = true
      console.log("🎤 Recording started with PCM16 format")
      toast.success("Recording started")
      return true

    } catch (error) {
      console.error("Failed to start recording:", error)
      toast.error(`Recording failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return false
    }
  }, [requestMicrophonePermission, initializeAudioContext])

  const stopRecording = useCallback(async () => {
    try {
      if (!isRecordingRef.current) {
        console.log("Not currently recording")
        return
      }

      isRecordingRef.current = false
      onAudioDataRef.current = null

      // Disconnect audio processing chain
      if (processorRef.current) {
        processorRef.current.disconnect()
        processorRef.current = null
      }

      if (sourceRef.current) {
        sourceRef.current.disconnect()
        sourceRef.current = null
      }

      console.log("🎤 Recording stopped")
      toast.success("Recording stopped")

    } catch (error) {
      console.error("Failed to stop recording:", error)
      toast.error("Failed to stop recording")
    }
  }, [])

  const startAudioProcessing = useCallback(
    (stream: MediaStream) => {
      try {
        if (!audioContextRef.current) {
          audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
        }

        const audioContext = audioContextRef.current

        if (!analyserRef.current) {
          analyserRef.current = audioContext.createAnalyser()
          analyserRef.current.fftSize = 256
          analyserRef.current.smoothingTimeConstant = 0.8
        }

        if (!sourceRef.current) {
          sourceRef.current = audioContext.createMediaStreamSource(stream)
          sourceRef.current.connect(analyserRef.current)
        }

        const bufferLength = analyserRef.current.frequencyBinCount
        const dataArray = new Uint8Array(bufferLength)

        const updateVolume = () => {
          if (analyserRef.current && isConversationActive) {
            analyserRef.current.getByteFrequencyData(dataArray)
            const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
            setAudioVolume(Math.min(average / 128, 1)) // Normalize and clamp to 0-1 range
            animationFrameRef.current = requestAnimationFrame(updateVolume)
          }
        }

        updateVolume()
      } catch (error) {
        console.error("Failed to start audio processing:", error)
        toast.error("Failed to initialize audio processing")
      }
    },
    [isConversationActive, setAudioVolume],
  )

  const stopAudioProcessing = useCallback(() => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }

    if (sourceRef.current) {
      sourceRef.current.disconnect()
      sourceRef.current = null
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop())
      streamRef.current = null
    }

    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current.close()
      audioContextRef.current = null
    }

    analyserRef.current = null
    setAudioVolume(0)
  }, [setAudioVolume])

  useEffect(() => {
    if (isConversationActive) {
      if (hasAudioPermission) {
        if (streamRef.current) {
          startAudioProcessing(streamRef.current)
        } else {
          requestMicrophonePermission().then((stream) => {
            if (stream) {
              startAudioProcessing(stream)
            }
          })
        }
      } else if (hasAudioPermission === null) {
        requestMicrophonePermission().then((stream) => {
          if (stream) {
            startAudioProcessing(stream)
          }
        })
      }
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      setAudioVolume(0)
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isConversationActive, hasAudioPermission, requestMicrophonePermission, startAudioProcessing, setAudioVolume])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopAudioProcessing()
    }
  }, [stopAudioProcessing])

  const initializeAudioWorklet = useCallback(async (): Promise<AudioWorkletNode | null> => {
    if (!audioContextRef.current) {
      console.warn("⚠️ Audio context not available for AudioWorklet")
      return null
    }

    try {
      // Load the audio worklet processor
      await audioContextRef.current.audioWorklet.addModule('/audio-worklet-processor.js')

      // Create the worklet node
      const workletNode = new AudioWorkletNode(audioContextRef.current, 'realtime-audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        channelCount: 1,
        channelCountMode: 'explicit',
        channelInterpretation: 'speakers'
      })

      workletNodeRef.current = workletNode
      console.log('✅ AudioWorklet initialized in audio manager')
      return workletNode

    } catch (error) {
      console.error('❌ Failed to initialize AudioWorklet in audio manager:', error)
      return null
    }
  }, [])

  return {
    requestMicrophonePermission,
    startRecording,
    stopRecording,
    isRecording: isRecordingRef.current,
    audioContext: audioContextRef.current,
    initializeAudioWorklet,
    workletNode: workletNodeRef.current
  }
}
