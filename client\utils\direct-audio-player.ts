/**
 * DirectAudioPlayer - Optimized PCM16 audio player ported from client1
 * Handles low-latency audio playback with queue management and error recovery
 */

interface AudioQueueItem {
  buffer: AudioBuffer
  chunkId?: string
  timestamp: number
}

export class DirectAudioPlayer {
  private sampleRate: number
  private isPlaying: boolean = false
  private audioContext: AudioContext | null = null
  private gainNode: GainNode | null = null
  private audioQueue: AudioQueueItem[] = []
  private isProcessingQueue: boolean = false
  private nextScheduledTime: number = 0
  private bufferDuration: number = 0.05 // 50ms buffer for lower latency
  private activeSources: Set<AudioBufferSourceNode> = new Set()
  private interruptFlag: boolean = false
  
  // Deduplication management
  private processedChunks: Set<string> = new Set()
  private maxProcessedChunks: number = 1000
  
  // Error handling
  private errorCount: number = 0
  private maxRetries: number = 3
  
  // Callbacks
  public onStart: (() => void) | null = null
  public onStop: (() => void) | null = null
  public onError: ((error: string) => void) | null = null

  constructor(sampleRate: number = 24000) {
    this.sampleRate = sampleRate
  }

  /**
   * Initialize the audio player with an AudioContext
   */
  async initialize(audioContext: AudioContext): Promise<void> {
    try {
      this.audioContext = audioContext
      
      // Create gain node for volume control
      this.gainNode = audioContext.createGain()
      this.gainNode.gain.value = 1.0
      this.gainNode.connect(audioContext.destination)
      
      // Reset scheduling
      this.nextScheduledTime = audioContext.currentTime
      
      console.log("🎵 DirectAudioPlayer initialized successfully")
    } catch (error) {
      console.error("❌ Failed to initialize DirectAudioPlayer:", error)
      throw error
    }
  }

  /**
   * Ensure AudioContext is running
   */
  private async ensureAudioContextRunning(): Promise<void> {
    if (!this.audioContext) {
      throw new Error("AudioContext not initialized")
    }
    
    if (this.audioContext.state === "suspended") {
      await this.audioContext.resume()
      console.log("🎵 AudioContext resumed")
    }
  }

  /**
   * Process audio data from base64 to AudioBuffer
   */
  async processAudioData(base64Data: string): Promise<AudioBuffer | null> {
    if (!this.audioContext) {
      throw new Error("AudioContext not initialized")
    }

    // Validate input data
    if (typeof base64Data !== "string" || base64Data.length === 0) {
      throw new Error("Invalid audio data: empty or non-string")
    }

    // Decode base64 data
    let binaryString: string
    try {
      binaryString = atob(base64Data)
    } catch (error) {
      throw new Error(`Base64 decode failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }

    if (binaryString.length === 0) {
      console.warn("⚠️ Empty audio data after base64 decode")
      return null
    }

    // Ensure even length for PCM16 (2 bytes per sample)
    if (binaryString.length % 2 !== 0) {
      console.warn("⚠️ Audio data length is not even (PCM16 requires 2 bytes per sample), padding with zero")
      binaryString += "\0"
    }

    const uint8Array = new Uint8Array(binaryString.length)
    for (let i = 0; i < binaryString.length; i++) {
      uint8Array[i] = binaryString.charCodeAt(i)
    }

    // Convert to Float32Array
    const int16Array = new Int16Array(uint8Array.buffer)
    const float32Array = new Float32Array(int16Array.length)

    // Use efficient conversion method
    let maxAmplitude = 0
    for (let i = 0; i < int16Array.length; i++) {
      const normalized = int16Array[i] / 32768.0
      float32Array[i] = normalized
      maxAmplitude = Math.max(maxAmplitude, Math.abs(normalized))
    }

    // Check if audio is too quiet
    if (maxAmplitude < 0.001) {
      console.debug("🔇 Audio appears to be silent")
    }

    // Create audio buffer
    const audioBuffer = this.audioContext.createBuffer(
      1,
      float32Array.length,
      this.sampleRate
    )
    audioBuffer.copyToChannel(float32Array, 0)

    return audioBuffer
  }

  /**
   * Play audio chunk with deduplication and queue management
   */
  async playChunk(base64Data: string, chunkId?: string): Promise<void> {
    try {
      // Deduplication check
      if (chunkId) {
        if (this.processedChunks.has(chunkId)) {
          console.debug(`🔄 Skipping duplicate audio chunk: ${chunkId}`)
          return
        }
        this.processedChunks.add(chunkId)

        // Cleanup old processed chunks
        if (this.processedChunks.size > this.maxProcessedChunks) {
          const chunksArray = Array.from(this.processedChunks)
          this.processedChunks = new Set(chunksArray.slice(-500))
        }
      }

      // Ensure AudioContext is running
      await this.ensureAudioContextRunning()

      // Process audio data
      const audioBuffer = await this.processAudioData(base64Data)
      if (!audioBuffer) {
        return
      }

      // Add to queue instead of playing directly
      this.audioQueue.push({
        buffer: audioBuffer,
        chunkId: chunkId,
        timestamp: Date.now(),
      })

      // Set playing state immediately for speech interruption detection
      if (!this.isPlaying) {
        this.isPlaying = true
        this.onStart?.()
      }

      // Start queue processing
      this.processAudioQueue()

      // Reset error count on success
      this.errorCount = 0
      console.debug(`✅ Audio chunk queued: ${audioBuffer.duration.toFixed(3)}s`)
    } catch (error) {
      this.errorCount++
      console.error(`❌ DirectAudioPlayer playback error (attempt ${this.errorCount}):`, error)

      if (this.errorCount < this.maxRetries) {
        console.log(`🔄 Retrying audio playback (${this.errorCount}/${this.maxRetries})`)
        // Short delay before retry
        setTimeout(() => {
          this.playChunk(base64Data, chunkId)
        }, 100 * this.errorCount)
      } else {
        console.error(`❌ Audio playback failed after ${this.maxRetries} attempts`)
        this.onError?.(error instanceof Error ? error.message : 'Unknown error')
        this.errorCount = 0 // Reset error count
      }
    }
  }

  /**
   * Process audio queue with proper scheduling
   */
  private async processAudioQueue(): Promise<void> {
    if (this.isProcessingQueue || this.audioQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    try {
      while (this.audioQueue.length > 0 && !this.interruptFlag) {
        const queueItem = this.audioQueue.shift()!
        await this.playAudioBuffer(queueItem.buffer)
      }
    } catch (error) {
      console.error("❌ Error processing audio queue:", error)
      this.onError?.(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      this.isProcessingQueue = false
      
      // Check if we should stop playing
      if (this.audioQueue.length === 0 && this.activeSources.size === 0) {
        this.isPlaying = false
        this.onStop?.()
      }
    }
  }

  /**
   * Play an AudioBuffer with proper scheduling
   */
  private async playAudioBuffer(audioBuffer: AudioBuffer): Promise<void> {
    if (!this.audioContext || !this.gainNode) {
      throw new Error("AudioContext or GainNode not initialized")
    }

    const source = this.audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(this.gainNode)

    // Track active source
    this.activeSources.add(source)

    // Schedule playback with minimal gap
    const currentTime = this.audioContext.currentTime
    let startTime: number

    if (this.nextScheduledTime <= currentTime) {
      // If we're behind schedule, start immediately
      startTime = currentTime
    } else {
      // Otherwise, start at the scheduled time
      startTime = this.nextScheduledTime
    }

    source.start(startTime)
    this.nextScheduledTime = startTime + audioBuffer.duration

    // Handle source end
    source.onended = () => {
      this.activeSources.delete(source)
      if (this.activeSources.size === 0 && this.audioQueue.length === 0) {
        this.isPlaying = false
        this.onStop?.()
      }
    }

    console.debug(`🎵 Playing audio buffer: ${audioBuffer.duration.toFixed(3)}s`)
  }

  /**
   * Interrupt all audio playback
   */
  interrupt(): void {
    console.log("🛑 DirectAudioPlayer interrupted")
    this.interruptFlag = true

    // Stop all active sources
    this.activeSources.forEach(source => {
      try {
        source.stop()
      } catch (e) {
        // Source might already be stopped
      }
    })

    this.activeSources.clear()
    this.audioQueue = []
    this.isPlaying = false
    this.isProcessingQueue = false
    this.interruptFlag = false

    this.onStop?.()
  }

  /**
   * Get current playing status
   */
  getStatus(): { isPlaying: boolean; queueLength: number; activeSources: number } {
    return {
      isPlaying: this.isPlaying,
      queueLength: this.audioQueue.length,
      activeSources: this.activeSources.size
    }
  }
}
