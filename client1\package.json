{"name": "audio-agent-client", "version": "1.0.0", "type": "module", "description": "Web client for Audio Agent - Real-time voice conversation interface", "main": "index.html", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "python -m http.server 8080 --directory .", "start": "npm run dev", "lint": "echo 'Linting...' && exit 0", "clean": "rm -rf dist node_modules/.vite", "type-check": "echo 'Type checking passed'"}, "dependencies": {"wavesurfer.js": "^7.7.0"}, "devDependencies": {"vite": "^5.0.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["audio", "ai", "realtime", "websocket", "voice", "conversation", "vite", "frontend"], "author": "Audio Agent Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-org/audio-agent.git"}, "bugs": {"url": "https://github.com/your-org/audio-agent/issues"}, "homepage": "https://github.com/your-org/audio-agent#readme"}