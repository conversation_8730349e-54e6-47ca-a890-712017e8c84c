"use client"

import { motion } from "framer-motion"
import { useAppStore } from "@/store/app-store"
import { DynamicMicIcon } from "@/components/conversation/dynamic-mic-icon"
import { useAudioRecording } from "@/hooks/use-audio-recording"
import { memo } from "react"
import { useTranslations } from 'next-intl'

export const ConversationButton = memo(() => {
  const { isConversationActive, toggleConversation, hasAudioPermission, connectionStatus } = useAppStore()
  const { isRecording, toggleRecording, requestMicrophonePermission } = useAudioRecording()
  const t = useTranslations('conversation')

  const handleClick = async () => {
    // Check connection status first
    if (connectionStatus !== "connected") {
      console.log("Cannot start recording - not connected to server")
      return
    }

    // If we need microphone permission, request it and then auto-start
    if (hasAudioPermission === null || hasAudioPermission === false) {
      const stream = await requestMicrophonePermission()
      if (!stream) {
        console.log("Microphone permission denied")
        return
      }
      // Permission granted, continue to start recording automatically
    }

    // Toggle recording only - don't use toggleConversation to avoid conflicts
    await toggleRecording()
  }

  return (
    <div className="flex flex-col items-center space-y-4">
      <motion.button
        onClick={handleClick}
        disabled={connectionStatus !== "connected" && hasAudioPermission !== false}
        className={`relative w-32 h-32 rounded-full clay-button flex items-center justify-center transition-all duration-300 ${
          isRecording
            ? "ring-4 ring-red-500 dark:ring-red-400"
            : isConversationActive
              ? "ring-4 ring-green-500 dark:ring-green-400"
              : "ring-4 ring-blue-500 dark:ring-blue-400"
        } ${connectionStatus !== "connected" && hasAudioPermission !== false ? "opacity-50 cursor-not-allowed" : ""}`}
        whileHover={{ scale: connectionStatus === "connected" ? 1.05 : 1 }}
        whileTap={{ scale: connectionStatus === "connected" ? 0.95 : 1 }}
        animate={isRecording ? { scale: [1, 1.02, 1] } : {}}
        transition={
          isRecording
            ? { duration: 1, repeat: Number.POSITIVE_INFINITY }
            : { type: "spring", stiffness: 400, damping: 17 }
        }
      >
        <DynamicMicIcon />

        {isRecording && (
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-red-500 dark:border-red-400"
            animate={{ scale: [1, 1.1, 1], opacity: [1, 0, 1] }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
          />
        )}
      </motion.button>

      <div className="text-center">
        <div className="font-medium">
          {hasAudioPermission === false
            ? t('grantMicrophoneAccess')
            : connectionStatus !== "connected"
              ? t('connecting')
              : isRecording
                ? t('recordingClickToStop')
                : t('clickToStartRecording')
          }
        </div>
        {hasAudioPermission === false && (
          <div className="text-sm text-red-500 mt-1">{t('microphoneAccessRequired')}</div>
        )}
        {connectionStatus !== "connected" && hasAudioPermission !== false && (
          <div className="text-sm text-yellow-500 mt-1">{t('waitingForServerConnection')}</div>
        )}
        {isRecording && (
          <div className="text-sm text-green-500 mt-1">{t('recordingActive')}</div>
        )}
      </div>
    </div>
  )
})

ConversationButton.displayName = "ConversationButton"
