import { renderHook, act } from '@testing-library/react'
import { useWebSocketManager } from '../../hooks/use-websocket-manager'
import { useAudioPlayback } from '../../hooks/use-audio-playback'

// Mock all dependencies
jest.mock('../../hooks/use-audio-playback')
jest.mock('../../hooks/use-session-manager')
jest.mock('../../hooks/use-error-handler')
jest.mock('../../hooks/use-performance-monitor')
jest.mock('../../hooks/use-transcription-buffer')
jest.mock('../../store/app-store')
jest.mock('../../store/chat-store')

describe('WebSocket Audio Flow Integration', () => {
  let mockWebSocket: any
  let mockAudioPlayback: any

  beforeEach(() => {
    mockWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      readyState: WebSocket.OPEN,
    }

    mockAudioPlayback = {
      playChunk: jest.fn().mockResolvedValue(true),
      handleChunkedAudio: jest.fn().mockResolvedValue(true),
      handleSpeechInterruption: jest.fn(),
      initializeAudioContext: jest.fn().mockResolvedValue({}),
    }

    ;(global.WebSocket as any) = jest.fn(() => mockWebSocket)
    ;(require('../../hooks/use-audio-playback').useAudioPlayback as jest.Mock).mockReturnValue(mockAudioPlayback)

    // Mock other hooks
    jest.mocked(require('../../hooks/use-session-manager').useSessionManager).mockReturnValue({
      currentSession: null,
      createSession: jest.fn(),
      endSession: jest.fn(),
      incrementMessageCount: jest.fn(),
      incrementAudioChunkCount: jest.fn(),
      updateConnectionQuality: jest.fn(),
      incrementConnectionAttempts: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-error-handler').useErrorHandler).mockReturnValue({
      handleConnectionError: jest.fn(),
      handleAudioError: jest.fn(),
      handleTranscriptionError: jest.fn(),
      handlePlaybackError: jest.fn(),
      resolveError: jest.fn(),
      retryError: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-performance-monitor').usePerformanceMonitor).mockReturnValue({
      updateMessageQueueSize: jest.fn(),
    })

    jest.mocked(require('../../hooks/use-transcription-buffer').useTranscriptionBuffer).mockReturnValue({
      handleTranscriptComplete: jest.fn(),
      handleTranscriptDelta: jest.fn(),
      clearAllBuffers: jest.fn(),
      transcriptStatus: 'Ready for speech...',
    })

    jest.mocked(require('../../store/app-store').useAppStore).mockReturnValue({
      connectionStatus: 'disconnected',
      setConnectionStatus: jest.fn(),
      conversationMode: 'standard',
      updateTranscriptStatus: jest.fn(),
      updateAIStatus: jest.fn(),
      clearTranscriptStatus: jest.fn(),
      clearAIStatus: jest.fn(),
    })

    jest.mocked(require('../../store/chat-store').useChatStore).mockReturnValue({
      addMessage: jest.fn(),
      updateStreamingMessage: jest.fn(),
      completeStreamingMessage: jest.fn(),
      currentStreamingMessageId: null,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should handle complete audio flow from WebSocket to playback', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    // Connect to WebSocket
    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving audio output message
    const audioMessage = {
      type: 'audio_output',
      data: btoa('test audio data'),
      format: 'pcm16',
      chunk_id: 'test-chunk-1',
    }

    // Simulate WebSocket message reception
    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(audioMessage) })
      }
    })

    // Verify audio playback was called
    expect(mockAudioPlayback.initializeAudioContext).toHaveBeenCalled()
    expect(mockAudioPlayback.playChunk).toHaveBeenCalledWith(
      audioMessage.data,
      audioMessage.chunk_id
    )
  })

  it('should handle chunked audio reassembly flow', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving chunked audio message
    const chunkedMessage = {
      type: 'audio_output_chunk',
      stream_id: 'stream-1',
      sequence: 0,
      total_chunks: 2,
      data: btoa('chunk data'),
      is_complete: false,
    }

    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(chunkedMessage) })
      }
    })

    expect(mockAudioPlayback.handleChunkedAudio).toHaveBeenCalledWith(
      chunkedMessage.stream_id,
      chunkedMessage.data,
      chunkedMessage.sequence,
      chunkedMessage.total_chunks,
      chunkedMessage.is_complete
    )
  })

  it('should handle interruption flow', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving interruption message
    const interruptionMessage = {
      type: 'interruption',
      status: 'start',
      interruption_type: 'barge_in',
    }

    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(interruptionMessage) })
      }
    })

    expect(mockAudioPlayback.handleSpeechInterruption).toHaveBeenCalled()
  })

  it('should handle transcript to chat flow', async () => {
    const mockAddMessage = jest.fn()
    jest.mocked(require('../../store/chat-store').useChatStore).mockReturnValue({
      addMessage: mockAddMessage,
      updateStreamingMessage: jest.fn(),
      completeStreamingMessage: jest.fn(),
      currentStreamingMessageId: null,
    })

    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving transcript complete message
    const transcriptMessage = {
      type: 'transcript_complete',
      text: 'Hello, this is a test transcript',
      source: 'paraformer',
      quality: 0.95,
      message_key: 'msg-123',
    }

    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(transcriptMessage) })
      }
    })

    // Verify transcript buffer handling was called
    const mockHandleTranscriptComplete = jest.mocked(
      require('../../hooks/use-transcription-buffer').useTranscriptionBuffer
    )().handleTranscriptComplete

    expect(mockHandleTranscriptComplete).toHaveBeenCalledWith(
      transcriptMessage.source,
      transcriptMessage.text,
      transcriptMessage.quality,
      transcriptMessage.message_key
    )
  })

  it('should handle AI response streaming flow', async () => {
    const mockAddMessage = jest.fn()
    const mockUpdateStreamingMessage = jest.fn()
    const mockCompleteStreamingMessage = jest.fn()

    jest.mocked(require('../../store/chat-store').useChatStore).mockReturnValue({
      addMessage: mockAddMessage,
      updateStreamingMessage: mockUpdateStreamingMessage,
      completeStreamingMessage: mockCompleteStreamingMessage,
      currentStreamingMessageId: null,
    })

    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving AI response message
    const aiResponseMessage = {
      type: 'ai_response',
      text: 'This is an AI response',
      complete: false,
      message_key: 'ai-msg-123',
    }

    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(aiResponseMessage) })
      }
    })

    expect(mockAddMessage).toHaveBeenCalledWith({
      role: 'assistant',
      content: aiResponseMessage.text,
      isStreaming: !aiResponseMessage.complete,
      messageKey: aiResponseMessage.message_key,
    })
  })

  it('should handle connection quality monitoring flow', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate receiving pong message
    const pongMessage = {
      type: 'pong',
      timestamp: Date.now(),
    }

    await act(async () => {
      const messageHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'message'
      )?.[1]

      if (messageHandler) {
        messageHandler({ data: JSON.stringify(pongMessage) })
      }
    })

    // Verify connection quality was updated
    const mockUpdateConnectionQuality = jest.mocked(
      require('../../hooks/use-session-manager').useSessionManager
    )().updateConnectionQuality

    expect(mockUpdateConnectionQuality).toHaveBeenCalled()
  })

  it('should handle error recovery flow', async () => {
    const mockHandleConnectionError = jest.fn()
    jest.mocked(require('../../hooks/use-error-handler').useErrorHandler).mockReturnValue({
      handleConnectionError: mockHandleConnectionError,
      handleAudioError: jest.fn(),
      handleTranscriptionError: jest.fn(),
      handlePlaybackError: jest.fn(),
      resolveError: jest.fn(),
      retryError: jest.fn(),
    })

    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    // Simulate WebSocket error
    await act(async () => {
      const errorHandler = mockWebSocket.addEventListener.mock.calls.find(
        call => call[0] === 'error'
      )?.[1]

      if (errorHandler) {
        errorHandler(new Error('Connection failed'))
      }
    })

    expect(mockHandleConnectionError).toHaveBeenCalled()
  })
})
