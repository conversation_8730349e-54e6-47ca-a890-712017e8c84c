import { renderHook, act } from '@testing-library/react'
import { useWebSocketManager } from '../use-websocket-manager'

// Mock dependencies
jest.mock('../use-audio-playback', () => ({
  useAudioPlayback: () => ({
    playChunk: jest.fn(),
    handleChunkedAudio: jest.fn(),
    handleSpeechInterruption: jest.fn(),
    initializeAudioContext: jest.fn().mockResolvedValue(undefined),
  }),
}))

jest.mock('../use-session-manager', () => ({
  useSessionManager: () => ({
    currentSession: null,
    createSession: jest.fn(),
    endSession: jest.fn(),
    incrementMessageCount: jest.fn(),
    incrementAudioChunkCount: jest.fn(),
    updateConnectionQuality: jest.fn(),
    incrementConnectionAttempts: jest.fn(),
  }),
}))

jest.mock('../use-error-handler', () => ({
  useErrorHandler: () => ({
    handleConnectionError: jest.fn(),
    handleAudioError: jest.fn(),
    handleTranscriptionError: jest.fn(),
    handlePlaybackError: jest.fn(),
    resolveError: jest.fn(),
    retryError: jest.fn(),
  }),
}))

jest.mock('../use-performance-monitor', () => ({
  usePerformanceMonitor: () => ({
    updateMessageQueueSize: jest.fn(),
  }),
}))

jest.mock('../use-transcription-buffer', () => ({
  useTranscriptionBuffer: () => ({
    handleTranscriptComplete: jest.fn(),
    handleTranscriptDelta: jest.fn(),
    clearAllBuffers: jest.fn(),
    transcriptStatus: 'Ready for speech...',
  }),
}))

jest.mock('@/store/app-store', () => ({
  useAppStore: () => ({
    connectionStatus: 'disconnected',
    setConnectionStatus: jest.fn(),
    conversationMode: 'standard',
    updateTranscriptStatus: jest.fn(),
    updateAIStatus: jest.fn(),
    clearTranscriptStatus: jest.fn(),
    clearAIStatus: jest.fn(),
  }),
}))

jest.mock('@/store/chat-store', () => ({
  useChatStore: () => ({
    addMessage: jest.fn(),
    updateStreamingMessage: jest.fn(),
    completeStreamingMessage: jest.fn(),
    currentStreamingMessageId: null,
  }),
}))

describe('useWebSocketManager', () => {
  let mockWebSocket: any

  beforeEach(() => {
    mockWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      readyState: WebSocket.OPEN,
    }
    
    // Mock WebSocket constructor
    ;(global.WebSocket as any) = jest.fn(() => mockWebSocket)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useWebSocketManager())

    expect(result.current.isConnected).toBe(false)
    expect(result.current.currentSession).toBeNull()
  })

  it('should connect to WebSocket server', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    await act(async () => {
      result.current.connect()
    })

    expect(global.WebSocket).toHaveBeenCalledWith('ws://localhost:8000/ws/conversation')
  })

  it('should send audio data when connected', async () => {
    const { result } = renderHook(() => useWebSocketManager())
    
    await act(async () => {
      result.current.connect()
    })

    const audioData = new ArrayBuffer(1024)
    
    await act(async () => {
      result.current.sendAudioData(audioData)
    })

    expect(mockWebSocket.send).toHaveBeenCalled()
  })

  it('should queue messages when disconnected', async () => {
    const { result } = renderHook(() => useWebSocketManager())

    const audioData = new ArrayBuffer(1024)
    
    await act(async () => {
      result.current.sendAudioData(audioData)
    })

    // Should not send immediately when disconnected
    expect(mockWebSocket.send).not.toHaveBeenCalled()
  })

  it('should handle mode selection', async () => {
    const { result } = renderHook(() => useWebSocketManager())
    
    await act(async () => {
      result.current.connect()
    })

    await act(async () => {
      const success = await result.current.sendModeSelection('interviewer')
      expect(success).toBe(true)
    })

    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining('"type":"mode_selection"')
    )
  })

  it('should handle manual speech end', async () => {
    const { result } = renderHook(() => useWebSocketManager())
    
    await act(async () => {
      result.current.connect()
    })

    await act(async () => {
      result.current.sendManualSpeechEnd()
    })

    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining('"type":"manual_speech_end"')
    )
  })

  it('should disconnect properly', async () => {
    const { result } = renderHook(() => useWebSocketManager())
    
    await act(async () => {
      result.current.connect()
    })

    await act(async () => {
      result.current.disconnect()
    })

    expect(mockWebSocket.close).toHaveBeenCalled()
  })

  it('should handle connection errors gracefully', async () => {
    const { result } = renderHook(() => useWebSocketManager())
    
    // Simulate connection error
    mockWebSocket.readyState = WebSocket.CLOSED
    
    await act(async () => {
      result.current.connect()
    })

    // Should handle error without throwing
    expect(result.current.isConnected).toBe(false)
  })
})
