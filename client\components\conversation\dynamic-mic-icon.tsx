"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON> } from "lucide-react"
import { useAppStore } from "@/store/app-store"

export function DynamicMicIcon() {
  const { isConversationActive, audioVolume, hasAudioPermission } = useAppStore()

  if (hasAudioPermission === false) {
    return (
      <motion.div
        className="text-red-500"
        animate={{ scale: [1, 1.05, 1] }}
        transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
      >
        <MicOff size={32} />
      </motion.div>
    )
  }

  return (
    <div className="relative">
      {/* Main microphone icon */}
      <motion.div
        className={`${isConversationActive ? "text-red-500" : "text-blue-500"}`}
        animate={
          isConversationActive
            ? {
                scale: [1, 1.05, 1],
                filter: ["brightness(1)", "brightness(1.1)", "brightness(1)"],
              }
            : {}
        }
        transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
      >
        <Mic size={32} />
      </motion.div>

      {/* Animated rings for active state */}
      {isConversationActive && (
        <>
          {[1, 2, 3].map((ring) => (
            <motion.div
              key={ring}
              className="absolute inset-0 rounded-full border-2 border-red-500/30"
              animate={{
                scale: [1, 1.3 + audioVolume * 0.3 + ring * 0.2],
                opacity: [0.6, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                delay: ring * 0.2,
                ease: "easeOut",
              }}
              style={{
                width: `${100 + ring * 15}%`,
                height: `${100 + ring * 15}%`,
                left: `${-ring * 7.5}%`,
                top: `${-ring * 7.5}%`,
              }}
            />
          ))}

          {/* Central pulsing effect */}
          <motion.div
            className="absolute inset-0 bg-red-500/20 rounded-full"
            animate={{
              scale: [1, 1.1 + audioVolume * 0.2],
              opacity: [0.3, 0.5],
            }}
            transition={{
              duration: 0.3,
              ease: "easeOut",
            }}
          />
        </>
      )}
    </div>
  )
}
