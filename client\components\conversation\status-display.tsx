"use client"

import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, <PERSON>, MessageSquare, Loader2 } from "lucide-react"
import { useChatStore } from "@/store/chat-store"
import { useAppStore } from "@/store/app-store"

export function StatusDisplay() {
  // Component disabled - return null to not render anything
  return null

      {/* Transcription Status */}
      <AnimatePresence>
        {transcriptStatus && transcriptStatus !== "Ready for speech..." && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-3 p-4 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Mic className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </motion.div>
            <div className="flex-1">
              <div className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Transcription
              </div>
              <div className="text-xs text-blue-600 dark:text-blue-400">
                {transcriptStatus}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Status */}
      <AnimatePresence>
        {aiStatus && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-3 p-4 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800"
          >
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Brain className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </motion.div>
            <div className="flex-1">
              <div className="text-sm font-medium text-purple-800 dark:text-purple-200">
                AI Processing
              </div>
              <div className="text-xs text-purple-600 dark:text-purple-400">
                {aiStatus}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Ready State */}
      <AnimatePresence>
        {connectionStatus === "connected" && 
         (!transcriptStatus || transcriptStatus === "Ready for speech...") && 
         !aiStatus && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-3 p-4 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800"
          >
            <motion.div
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <MessageSquare className="w-5 h-5 text-green-600 dark:text-green-400" />
            </motion.div>
            <div className="flex-1">
              <div className="text-sm font-medium text-green-800 dark:text-green-200">
                Ready
              </div>
              <div className="text-xs text-green-600 dark:text-green-400">
                Waiting for your voice input...
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>


    </div>
  )
}
