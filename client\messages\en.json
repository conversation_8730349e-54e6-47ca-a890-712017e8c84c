{"common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "close": "Close"}, "header": {"title": "Audio Agent", "subtitle": "AI Assistant"}, "language": {"english": "English", "chinese": "中文", "japanese": "日本語", "selectLanguage": "Select Language"}, "welcome": {"title": "Welcome to Audio Agent", "subtitle": "AI Assistant", "description": "I am your AI assistant, ready to answer questions and provide assistance."}, "features": {"smartQA": "Smart Q&A", "realTimeChat": "Real-time Conversation", "multiDomain": "Multi-domain Knowledge", "personalized": "Personalized Service"}, "tips": {"microphonePermission": "Tip: First-time use requires microphone permission", "realTimeSupport": "Supports real-time voice conversation and text display", "switchMode": "Switch conversation mode anytime"}, "voice": {"startRecording": "Start Recording", "stopRecording": "Stop Recording", "listening": "Listening...", "processing": "Processing...", "speaking": "Speaking..."}, "chat": {"typeMessage": "Type your message...", "send": "Send", "newConversation": "New Conversation", "clearHistory": "Clear History"}, "settings": {"title": "Settings", "theme": "Theme", "language": "Language", "voice": "Voice Settings", "notifications": "Notifications"}, "errors": {"microphoneAccess": "Unable to access microphone", "networkError": "Network connection error", "serverError": "Server error, please try again later", "unknownError": "Unknown error occurred"}, "conversation": {"grantMicrophoneAccess": "Grant Microphone Access", "connecting": "Connecting...", "recordingClickToStop": "Recording - Click to Stop", "clickToStartRecording": "Click to Start Recording", "microphoneAccessRequired": "Microphone access required", "waitingForServerConnection": "Waiting for server connection", "recordingActive": "🎤 Recording active"}, "voiceControl": {"title": "Audio Agent", "subtitle": "Advanced AI Voice Assistant", "instruction": "Click the button to start or stop the conversation"}, "chatInterface": {"title": "Conversation", "messageCount": "message", "messageCountPlural": "messages", "clearAllMessages": "Clear all messages", "chatAlreadyEmpty": "Chat is already empty", "clearedMessages": "Cleared {count} messages"}, "connectionStatus": {"connected": "Connected", "connecting": "Connecting", "disconnected": "Disconnected", "connectionError": "Connection Error", "unknown": "Unknown", "connectedDescription": "Connected to Audio Agent server", "connectingDescription": "Connecting to Audio Agent server...", "disconnectedDescription": "Not connected to server", "errorDescription": "Failed to connect to server", "unknownDescription": "Connection status unknown", "connectedDetailedDescription": "Successfully connected to Audio Agent server", "connectingDetailedDescription": "Establishing connection to Audio Agent server...", "disconnectedDetailedDescription": "Not connected to server. Click to reconnect.", "errorDetailedDescription": "Failed to connect to server. Check your connection.", "connectionActive": "Connection active", "sessionActive": "Session active", "session": "Session"}, "modeSelector": {"standardTitle": "Standard Assistant", "standardDescription": "General AI assistant for various questions", "interviewerTitle": "Interviewer <PERSON>", "interviewerDescription": "Professional interviewer for technical interviews", "modeSwitchedTo": "Mode switched to: {mode}", "failedToUpdateMode": "Failed to update mode on server", "modeSetTo": "Mode set to: {mode}", "modeWillBeApplied": "Mode will be applied when connected to server", "failedToSwitchMode": "Failed to switch mode"}, "errorDisplay": {"retries": "Retries: {count}", "moreErrors": "+{count} more errors", "manuallyResolved": "Manually resolved", "active": "Active: {count}", "total": "Total: {count}", "resolved": "Resolved: {count}", "clearAll": "Clear All"}, "settingsPanel": {"title": "Settings", "audio": "Audio", "interface": "Interface", "advanced": "Advanced", "exportSettings": "Export Settings", "importSettings": "Import Settings", "resetSettings": "Reset Settings", "settingsExported": "Settings exported successfully", "settingsImported": "Settings imported successfully", "invalidSettingsFile": "Invalid settings file", "settingsReset": "Settings reset to defaults", "confirmReset": "Are you sure you want to reset all settings?", "cancel": "Cancel", "reset": "Reset"}, "chatMessages": {"startConversation": "Start a conversation", "messagesWillAppearHere": "Your messages will appear here"}, "debugPanel": {"title": "Debug Panel", "debug": "Debug", "debugDataExported": "Debug data exported successfully", "copiedToClipboard": "Copied to clipboard", "failedToCopy": "Failed to copy to clipboard", "debugLogsCleared": "Debug logs cleared", "allCategories": "All Categories", "websocket": "WebSocket", "audio": "Audio", "session": "Session", "performance": "Performance", "ui": "UI", "general": "General"}, "themeToggle": {"toggleTheme": "Toggle theme"}, "toast": {"recordingStopped": "Recording stopped", "errorResolved": "Error resolved: {resolution}", "issueFixed": "Issue fixed", "clearedTypeErrors": "Cleared {type} errors", "allErrorsCleared": "All errors cleared", "appUpdateAvailable": "App update available! Refresh to get the latest version.", "refresh": "Refresh"}}