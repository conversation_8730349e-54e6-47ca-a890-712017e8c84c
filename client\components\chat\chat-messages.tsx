"use client"

import { useEffect, useRef, memo, use<PERSON><PERSON>back, useMemo } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useChatStore } from "@/store/chat-store"
import { <PERSON>r, <PERSON><PERSON>, MessageCircle } from "lucide-react"
import { useTranslations } from 'next-intl'

const MessageAvatar = memo(({ role }: { role: "user" | "assistant" }) => (
  <div
    className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${
      role === "user"
        ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white"
        : "bg-gradient-to-br from-green-500 to-teal-600 text-white"
    }`}
  >
    {role === "user" ? <User size={16} /> : <Bot size={16} />}
  </div>
))

MessageAvatar.displayName = "MessageAvatar"

const EmptyState = memo(() => {
  const t = useTranslations('chatMessages')

  return (
    <div className="flex items-center justify-center h-full text-muted-foreground">
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-orange-500/20 to-amber-500/20 flex items-center justify-center"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
        >
          <MessageCircle size={32} className="text-orange-500" />
        </motion.div>
        <p className="text-lg font-medium">{t('startConversation')}</p>
        <p className="text-sm opacity-60 mt-1">{t('messagesWillAppearHere')}</p>
      </motion.div>
    </div>
  )
})

EmptyState.displayName = "EmptyState"

const TypingIndicator = memo(() => (
  <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="flex items-start space-x-3">
    <MessageAvatar role="assistant" />
    <div className="clay-button p-4 bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-950/50 dark:to-teal-950/50">
      <div className="flex space-x-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-green-500 rounded-full"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.2,
              repeat: Number.POSITIVE_INFINITY,
              delay: i * 0.2,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
    </div>
  </motion.div>
))

TypingIndicator.displayName = "TypingIndicator"

const MessageBubble = memo(({ message, index }: { message: any; index: number }) => {
  const formattedTime = useMemo(() => new Date(message.timestamp).toLocaleTimeString(), [message.timestamp])

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{
        duration: 0.3,
        delay: Math.min(index * 0.02, 0.1),
        type: "spring",
        stiffness: 400,
        damping: 25,
      }}
      className={`flex items-start space-x-3 ${message.role === "user" ? "flex-row-reverse space-x-reverse" : ""}`}
    >
      <MessageAvatar role={message.role} />

      <div className={`flex-1 max-w-[80%] ${message.role === "user" ? "text-right" : ""}`}>
        <motion.div
          className={`clay-button p-4 relative overflow-hidden ${
            message.role === "user"
              ? "bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50"
              : "bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-950/50 dark:to-teal-950/50"
          }`}
          whileHover={{ scale: 1.01, y: -1 }}
          transition={{ type: "spring", stiffness: 400, damping: 17 }}
        >
          <p className="text-sm leading-relaxed">{message.content}</p>
          {message.isStreaming && (
            <motion.div
              className="inline-block w-2 h-4 bg-current ml-1 rounded-sm"
              animate={{ opacity: [1, 0] }}
              transition={{ duration: 0.8, repeat: Number.POSITIVE_INFINITY }}
            />
          )}
        </motion.div>
        <div className="text-xs text-muted-foreground mt-2 px-1">{formattedTime}</div>
      </div>
    </motion.div>
  )
})

MessageBubble.displayName = "MessageBubble"

export const ChatMessages = memo(() => {
  const { messages, isAssistantTyping } = useChatStore()
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Debug logging for message state
  useEffect(() => {
    console.log("🎯 ChatMessages render - messages count:", messages.length, "isAssistantTyping:", isAssistantTyping)
    messages.forEach((msg, idx) => {
      console.log(`🎯 Message ${idx}:`, {
        id: msg.id,
        role: msg.role,
        content: msg.content.substring(0, 50) + (msg.content.length > 50 ? "..." : ""),
        isStreaming: msg.isStreaming,
        messageKey: msg.messageKey
      })
    })
  }, [messages, isAssistantTyping])

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [])

  useEffect(() => {
    const timeoutId = setTimeout(scrollToBottom, 100)
    return () => clearTimeout(timeoutId)
  }, [messages.length, isAssistantTyping, scrollToBottom])

  if (messages.length === 0) {
    return <EmptyState />
  }

  return (
    <div className="h-full overflow-y-auto space-y-4 pr-2 custom-scrollbar">
      <AnimatePresence mode="popLayout">
        {messages.map((message, index) => (
          <MessageBubble key={message.id} message={message} index={index} />
        ))}
      </AnimatePresence>

      {isAssistantTyping && <TypingIndicator />}

      <div ref={messagesEndRef} />
    </div>
  )
})

ChatMessages.displayName = "ChatMessages"
