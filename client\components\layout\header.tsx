"use client"

import { memo } from "react"
import { motion } from "framer-motion"
import Image from "next/image"
import { ThemeToggle } from "@/components/settings/theme-toggle"
import { LanguageToggle } from "@/components/settings/language-toggle"
import { useTranslations } from 'next-intl'

const Logo = memo(() => {
  const t = useTranslations('header')

  return (
    <motion.div
      className="flex items-center space-x-3"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <motion.div
        className="relative w-10 h-10 rounded-xl overflow-hidden"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <Image src="/audio_manus_logo.jpeg" alt="Audio Manus" fill className="object-cover" />
      </motion.div>
      <div>
        <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          {t('title')}
        </h1>
        <p className="text-sm text-muted-foreground">{t('subtitle')}</p>
      </div>
    </motion.div>
  )
})

Logo.displayName = "Logo"

export const Header = memo(() => {
  return (
    <header className="glass-card sticky top-0 z-50 border-b border-white/10 dark:border-gray-800/10">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Logo />

          {/* Controls Section */}
          <motion.div
            className="flex items-center space-x-4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, ease: "easeOut", delay: 0.1 }}
          >
            <ThemeToggle />
            <LanguageToggle />
          </motion.div>
        </div>
      </div>
    </header>
  )
})

Header.displayName = "Header"
