/**
 * AudioWorklet Processor for Real-time Audio Processing
 * Provides advanced audio processing capabilities for Audio Agent
 * 
 * Note: This file must be in JavaScript (not TypeScript) as it runs in the AudioWorklet context
 */

class RealtimeAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();

    // Audio processing parameters
    this.bufferSize = 4096;
    this.inputBuffer = new Float32Array(this.bufferSize);
    this.outputBuffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;

    // Audio enhancement settings
    this.gainControl = 1.0;
    this.noiseGate = 0.01;
    this.compressionRatio = 2.0;
    this.isEnabled = true;

    // Processing statistics
    this.processedSamples = 0;
    this.droppedFrames = 0;

    // Listen for parameter changes from main thread
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    // Send ready signal
    this.port.postMessage({
      type: "processor-ready",
      timestamp: currentTime,
    });
  }

  /**
   * Handle messages from main thread
   * @param {Object} data Message data
   */
  handleMessage(data) {
    switch (data.type) {
      case "set-gain":
        this.gainControl = Math.max(0, Math.min(2.0, data.value));
        break;

      case "set-noise-gate":
        this.noiseGate = Math.max(0, Math.min(1.0, data.value));
        break;

      case "set-compression":
        this.compressionRatio = Math.max(1.0, Math.min(10.0, data.value));
        break;

      case "toggle-processing":
        this.isEnabled = data.enabled;
        break;

      case "reset-stats":
        this.processedSamples = 0;
        this.droppedFrames = 0;
        break;

      default:
        console.warn("Unknown message type:", data.type);
    }
  }

  /**
   * Apply noise gate to reduce background noise
   * @param {Float32Array} buffer Audio buffer
   * @param {number} threshold Noise gate threshold
   */
  applyNoiseGate(buffer, threshold) {
    for (let i = 0; i < buffer.length; i++) {
      const amplitude = Math.abs(buffer[i]);
      if (amplitude < threshold) {
        buffer[i] *= amplitude / threshold; // Gradual reduction instead of hard cut
      }
    }
  }

  /**
   * Apply dynamic range compression
   * @param {Float32Array} buffer Audio buffer
   * @param {number} ratio Compression ratio
   */
  applyCompression(buffer, ratio) {
    const threshold = 0.7;
    for (let i = 0; i < buffer.length; i++) {
      const amplitude = Math.abs(buffer[i]);
      if (amplitude > threshold) {
        const excess = amplitude - threshold;
        const compressedExcess = excess / ratio;
        const sign = buffer[i] >= 0 ? 1 : -1;
        buffer[i] = sign * (threshold + compressedExcess);
      }
    }
  }

  /**
   * Apply gain control
   * @param {Float32Array} buffer Audio buffer
   * @param {number} gain Gain multiplier
   */
  applyGain(buffer, gain) {
    for (let i = 0; i < buffer.length; i++) {
      buffer[i] *= gain;
      // Prevent clipping
      buffer[i] = Math.max(-1.0, Math.min(1.0, buffer[i]));
    }
  }

  /**
   * Calculate RMS (Root Mean Square) for volume analysis
   * @param {Float32Array} buffer Audio buffer
   * @returns {number} RMS value
   */
  calculateRMS(buffer) {
    let sum = 0;
    for (let i = 0; i < buffer.length; i++) {
      sum += buffer[i] * buffer[i];
    }
    return Math.sqrt(sum / buffer.length);
  }

  /**
   * Main audio processing function
   * @param {Float32Array[][]} inputs Input audio channels
   * @param {Float32Array[][]} outputs Output audio channels
   * @param {Object} parameters Audio parameters
   * @returns {boolean} Keep processor alive
   */
  process(inputs, outputs, parameters) {
    try {
      const input = inputs[0];
      const output = outputs[0];

      // If no input or processing is disabled, pass through
      if (!input || !input[0] || !this.isEnabled) {
        if (output && output[0]) {
          output[0].fill(0); // Output silence
        }
        return true;
      }

      const inputChannel = input[0];
      const outputChannel = output[0];

      // Copy input to output buffer for processing
      for (let i = 0; i < inputChannel.length; i++) {
        outputChannel[i] = inputChannel[i];
      }

      // Apply audio processing chain
      if (this.noiseGate > 0) {
        this.applyNoiseGate(outputChannel, this.noiseGate);
      }

      if (this.compressionRatio > 1.0) {
        this.applyCompression(outputChannel, this.compressionRatio);
      }

      if (this.gainControl !== 1.0) {
        this.applyGain(outputChannel, this.gainControl);
      }

      // Calculate and send audio statistics periodically
      this.processedSamples += inputChannel.length;
      if (this.processedSamples % 4800 === 0) { // Every ~100ms at 48kHz
        const rms = this.calculateRMS(outputChannel);
        const peak = Math.max(...outputChannel.map(Math.abs));
        
        this.port.postMessage({
          type: "audio-stats",
          rms: rms,
          peak: peak,
          processedSamples: this.processedSamples,
          droppedFrames: this.droppedFrames,
          timestamp: currentTime
        });
      }

      return true;
    } catch (error) {
      this.droppedFrames++;
      console.error("Audio processing error:", error);
      return true; // Keep processor alive even on error
    }
  }

  /**
   * Called when the processor is being destroyed
   */
  static get parameterDescriptors() {
    return [
      {
        name: 'gain',
        defaultValue: 1.0,
        minValue: 0.0,
        maxValue: 2.0,
        automationRate: 'a-rate'
      },
      {
        name: 'noiseGate',
        defaultValue: 0.01,
        minValue: 0.0,
        maxValue: 1.0,
        automationRate: 'k-rate'
      }
    ];
  }
}

// Register the processor
registerProcessor('realtime-audio-processor', RealtimeAudioProcessor);

console.log('🎵 RealtimeAudioProcessor registered successfully');
