"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { AlertTriangle, X, Refresh<PERSON><PERSON>, ChevronDown, ChevronUp } from "lucide-react"
import { useError<PERSON>and<PERSON> } from "@/hooks/use-error-handler"
import { Button } from "@/components/ui/button"
import { useTranslations } from 'next-intl'

export function ErrorDisplay() {
  const {
    getActiveErrors,
    getCriticalErrors,
    getErrorStats,
    resolveError,
    retryError,
    clearErrors,
    getSeverityColor,
    getSeverityIcon
  } = useErrorHandler()

  const [isExpanded, setIsExpanded] = useState(false)
  const t = useTranslations('errorDisplay')
  const activeErrors = getActiveErrors()
  const criticalErrors = getCriticalErrors()
  const stats = getErrorStats()

  if (activeErrors.length === 0) {
    return null
  }

  const hasMultipleErrors = activeErrors.length > 1
  const displayError = criticalErrors.length > 0 ? criticalErrors[0] : activeErrors[0]

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="mb-4"
      >
        {/* Main Error Display */}
        <div className={`p-4 rounded-lg border ${
          displayError.severity === "critical" 
            ? "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800"
            : displayError.severity === "high"
              ? "bg-orange-50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800"
              : "bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800"
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className={getSeverityColor(displayError.severity)}
              >
                <AlertTriangle className="w-5 h-5" />
              </motion.div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm font-medium">
                    {getSeverityIcon(displayError.severity)} {displayError.message}
                  </span>
                  <span className="text-xs px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 capitalize">
                    {displayError.type}
                  </span>
                </div>
                
                <div className="text-xs text-muted-foreground">
                  {displayError.timestamp.toLocaleTimeString()}
                  {displayError.retryCount > 0 && (
                    <span className="ml-2">• {t('retries', { count: displayError.retryCount })}</span>
                  )}
                  {hasMultipleErrors && (
                    <span className="ml-2">• {t('moreErrors', { count: activeErrors.length - 1 })}</span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Retry Button */}
              <Button
                size="sm"
                variant="outline"
                onClick={() => retryError(displayError.id)}
                className="h-8 px-2"
              >
                <RefreshCw className="w-3 h-3" />
              </Button>

              {/* Resolve Button */}
              <Button
                size="sm"
                variant="outline"
                onClick={() => resolveError(displayError.id, t('manuallyResolved'))}
                className="h-8 px-2"
              >
                <X className="w-3 h-3" />
              </Button>

              {/* Expand/Collapse Button */}
              {hasMultipleErrors && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="h-8 px-2"
                >
                  {isExpanded ? (
                    <ChevronUp className="w-3 h-3" />
                  ) : (
                    <ChevronDown className="w-3 h-3" />
                  )}
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Expanded Error List */}
        <AnimatePresence>
          {isExpanded && hasMultipleErrors && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-2 space-y-2"
            >
              {activeErrors.slice(1).map((error) => (
                <motion.div
                  key={error.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 flex-1">
                      <span className={`text-sm ${getSeverityColor(error.severity)}`}>
                        {getSeverityIcon(error.severity)}
                      </span>
                      <span className="text-sm">{error.message}</span>
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 capitalize">
                        {error.type}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => retryError(error.id)}
                        className="h-6 px-1"
                      >
                        <RefreshCw className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => resolveError(error.id, t('manuallyResolved'))}
                        className="h-6 px-1"
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error Stats and Actions */}
        {activeErrors.length > 2 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                {t('active', { count: stats.activeErrors })} • {t('total', { count: stats.totalErrors })} • {t('resolved', { count: stats.resolvedErrors })}
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={() => clearErrors()}
                className="h-6 px-2 text-xs"
              >
                {t('clearAll')}
              </Button>
            </div>
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}
