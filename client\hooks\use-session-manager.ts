"use client"

import { useCallback, useEffect, useRef, useState } from "react"
import { toast } from "sonner"

export interface SessionInfo {
  sessionId: string
  startTime: Date
  lastActivity: Date
  messageCount: number
  audioChunkCount: number
  connectionAttempts: number
  isActive: boolean
  mode: string
  quality: "good" | "poor" | "unstable" | "disconnected"
}

export interface SessionStats {
  totalSessions: number
  currentSession: SessionInfo | null
  averageSessionDuration: number
  totalMessages: number
  totalAudioChunks: number
}

/**
 * Comprehensive session management hook
 * Handles session lifecycle, persistence, and statistics
 */
export const useSessionManager = () => {
  const [currentSession, setCurrentSession] = useState<SessionInfo | null>(null)
  const [sessionHistory, setSessionHistory] = useState<SessionInfo[]>([])
  const sessionStatsRef = useRef<SessionStats>({
    totalSessions: 0,
    currentSession: null,
    averageSessionDuration: 0,
    totalMessages: 0,
    totalAudioChunks: 0
  })

  // Load session data from localStorage on mount
  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem("audioAgent_sessionHistory")
      if (savedHistory) {
        const history = JSON.parse(savedHistory).map((session: any) => ({
          ...session,
          startTime: new Date(session.startTime),
          lastActivity: new Date(session.lastActivity)
        }))
        setSessionHistory(history)
        
        // Update stats
        sessionStatsRef.current.totalSessions = history.length
        sessionStatsRef.current.totalMessages = history.reduce((sum: number, s: SessionInfo) => sum + s.messageCount, 0)
        sessionStatsRef.current.totalAudioChunks = history.reduce((sum: number, s: SessionInfo) => sum + s.audioChunkCount, 0)
        
        if (history.length > 0) {
          const totalDuration = history.reduce((sum: number, s: SessionInfo) => {
            return sum + (s.lastActivity.getTime() - s.startTime.getTime())
          }, 0)
          sessionStatsRef.current.averageSessionDuration = totalDuration / history.length
        }
      }
    } catch (error) {
      console.error("Failed to load session history:", error)
    }
  }, [])

  // Save session data to localStorage
  const saveSessionHistory = useCallback((history: SessionInfo[]) => {
    try {
      localStorage.setItem("audioAgent_sessionHistory", JSON.stringify(history))
    } catch (error) {
      console.error("Failed to save session history:", error)
    }
  }, [])

  const createSession = useCallback((sessionId: string, mode: string = "standard") => {
    const newSession: SessionInfo = {
      sessionId,
      startTime: new Date(),
      lastActivity: new Date(),
      messageCount: 0,
      audioChunkCount: 0,
      connectionAttempts: 1,
      isActive: true,
      mode,
      quality: "good"
    }

    setCurrentSession(newSession)
    sessionStatsRef.current.currentSession = newSession
    sessionStatsRef.current.totalSessions++

    console.log("🎯 Session created:", sessionId)
    toast.success(`Session started: ${sessionId.substring(0, 8)}...`)

    return newSession
  }, [])

  const updateSession = useCallback((updates: Partial<SessionInfo>) => {
    if (!currentSession) return

    const updatedSession = {
      ...currentSession,
      ...updates,
      lastActivity: new Date()
    }

    setCurrentSession(updatedSession)
    sessionStatsRef.current.currentSession = updatedSession

    // Update stats
    if (updates.messageCount !== undefined) {
      sessionStatsRef.current.totalMessages += (updates.messageCount - currentSession.messageCount)
    }
    if (updates.audioChunkCount !== undefined) {
      sessionStatsRef.current.totalAudioChunks += (updates.audioChunkCount - currentSession.audioChunkCount)
    }
  }, [currentSession])

  const endSession = useCallback((reason: string = "normal") => {
    if (!currentSession) return

    const endedSession = {
      ...currentSession,
      isActive: false,
      lastActivity: new Date()
    }

    // Add to history
    const newHistory = [...sessionHistory, endedSession]
    setSessionHistory(newHistory)
    saveSessionHistory(newHistory)

    // Calculate session duration
    const duration = endedSession.lastActivity.getTime() - endedSession.startTime.getTime()
    
    // Update average duration
    const totalDuration = sessionStatsRef.current.averageSessionDuration * (sessionStatsRef.current.totalSessions - 1) + duration
    sessionStatsRef.current.averageSessionDuration = totalDuration / sessionStatsRef.current.totalSessions

    setCurrentSession(null)
    sessionStatsRef.current.currentSession = null

    console.log("🏁 Session ended:", endedSession.sessionId, "Duration:", Math.round(duration / 1000), "seconds")
    toast.info(`Session ended (${Math.round(duration / 1000)}s): ${reason}`)

    return endedSession
  }, [currentSession, sessionHistory, saveSessionHistory])

  const incrementMessageCount = useCallback(() => {
    updateSession({ messageCount: (currentSession?.messageCount || 0) + 1 })
  }, [currentSession, updateSession])

  const incrementAudioChunkCount = useCallback(() => {
    updateSession({ audioChunkCount: (currentSession?.audioChunkCount || 0) + 1 })
  }, [currentSession, updateSession])

  const updateConnectionQuality = useCallback((quality: SessionInfo["quality"]) => {
    updateSession({ quality })
  }, [updateSession])

  const incrementConnectionAttempts = useCallback(() => {
    updateSession({ connectionAttempts: (currentSession?.connectionAttempts || 0) + 1 })
  }, [currentSession, updateSession])

  const getSessionStats = useCallback((): SessionStats => {
    return { ...sessionStatsRef.current }
  }, [])

  const clearSessionHistory = useCallback(() => {
    setSessionHistory([])
    localStorage.removeItem("audioAgent_sessionHistory")
    sessionStatsRef.current = {
      totalSessions: 0,
      currentSession: currentSession,
      averageSessionDuration: 0,
      totalMessages: 0,
      totalAudioChunks: 0
    }
    toast.success("Session history cleared")
  }, [currentSession])

  const getSessionDuration = useCallback(() => {
    if (!currentSession) return 0
    return Date.now() - currentSession.startTime.getTime()
  }, [currentSession])

  const formatSessionDuration = useCallback((duration: number) => {
    const seconds = Math.floor(duration / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }, [])

  return {
    currentSession,
    sessionHistory,
    createSession,
    updateSession,
    endSession,
    incrementMessageCount,
    incrementAudioChunkCount,
    updateConnectionQuality,
    incrementConnectionAttempts,
    getSessionStats,
    clearSessionHistory,
    getSessionDuration,
    formatSessionDuration
  }
}
