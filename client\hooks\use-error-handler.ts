"use client"

import { useCallback, useRef, useState } from "react"
import { toast } from "sonner"
import { useTranslations } from 'next-intl'

export interface ErrorInfo {
  id: string
  type: "connection" | "audio" | "transcription" | "playback" | "session" | "unknown"
  message: string
  details?: any
  timestamp: Date
  resolved: boolean
  retryCount: number
  severity: "low" | "medium" | "high" | "critical"
}

export interface ErrorStats {
  totalErrors: number
  resolvedErrors: number
  activeErrors: number
  errorsByType: Record<string, number>
  lastError: ErrorInfo | null
}

/**
 * Comprehensive error handling and recovery system
 */
export const useErrorHandler = () => {
  const [errors, setErrors] = useState<ErrorInfo[]>([])
  const [isRecovering, setIsRecovering] = useState(false)
  const errorCountRef = useRef(0)
  const maxRetries = 3
  const t = useTranslations('toast')
  const maxStoredErrors = 100

  const generateErrorId = useCallback(() => {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }, [])

  const getSeverityColor = useCallback((severity: ErrorInfo["severity"]) => {
    switch (severity) {
      case "low": return "text-blue-500"
      case "medium": return "text-yellow-500"
      case "high": return "text-orange-500"
      case "critical": return "text-red-500"
      default: return "text-gray-500"
    }
  }, [])

  const getSeverityIcon = useCallback((severity: ErrorInfo["severity"]) => {
    switch (severity) {
      case "low": return "ℹ️"
      case "medium": return "⚠️"
      case "high": return "🚨"
      case "critical": return "💥"
      default: return "❓"
    }
  }, [])

  const logError = useCallback((
    type: ErrorInfo["type"],
    message: string,
    details?: any,
    severity: ErrorInfo["severity"] = "medium"
  ): string => {
    const errorId = generateErrorId()
    const error: ErrorInfo = {
      id: errorId,
      type,
      message,
      details,
      timestamp: new Date(),
      resolved: false,
      retryCount: 0,
      severity
    }

    setErrors(prev => {
      const newErrors = [error, ...prev].slice(0, maxStoredErrors)
      return newErrors
    })

    errorCountRef.current++

    // Log to console with appropriate level
    const logMessage = `[${type.toUpperCase()}] ${message}`
    switch (severity) {
      case "low":
        console.info(logMessage, details)
        break
      case "medium":
        console.warn(logMessage, details)
        break
      case "high":
      case "critical":
        console.error(logMessage, details)
        break
    }

    // Show toast notification
    const toastMessage = `${getSeverityIcon(severity)} ${message}`
    switch (severity) {
      case "low":
        toast.info(toastMessage, { duration: 3000 })
        break
      case "medium":
        toast.warning(toastMessage, { duration: 5000 })
        break
      case "high":
        toast.error(toastMessage, { duration: 7000 })
        break
      case "critical":
        toast.error(toastMessage, { duration: 10000 })
        break
    }

    return errorId
  }, [generateErrorId, getSeverityIcon])

  const resolveError = useCallback((errorId: string, resolution?: string) => {
    setErrors(prev => prev.map(error => 
      error.id === errorId 
        ? { ...error, resolved: true, details: { ...error.details, resolution } }
        : error
    ))

    console.log(`✅ Error resolved: ${errorId}`, resolution)
    toast.success(t('errorResolved', { resolution: resolution || t('issueFixed') }))
  }, [])

  const retryError = useCallback((errorId: string, retryFn?: () => Promise<boolean>) => {
    const error = errors.find(e => e.id === errorId)
    if (!error) return false

    if (error.retryCount >= maxRetries) {
      logError(error.type, `Max retries exceeded for: ${error.message}`, error.details, "critical")
      return false
    }

    setErrors(prev => prev.map(e => 
      e.id === errorId 
        ? { ...e, retryCount: e.retryCount + 1 }
        : e
    ))

    if (retryFn) {
      setIsRecovering(true)
      retryFn()
        .then(success => {
          if (success) {
            resolveError(errorId, "Retry successful")
          } else {
            logError(error.type, `Retry failed for: ${error.message}`, error.details, "high")
          }
        })
        .catch(retryError => {
          logError(error.type, `Retry error: ${retryError.message}`, retryError, "high")
        })
        .finally(() => {
          setIsRecovering(false)
        })
    }

    return true
  }, [errors, logError, resolveError, maxRetries])

  const clearErrors = useCallback((type?: ErrorInfo["type"]) => {
    if (type) {
      setErrors(prev => prev.filter(error => error.type !== type))
      toast.success(t('clearedTypeErrors', { type }))
    } else {
      setErrors([])
      toast.success(t('allErrorsCleared'))
    }
  }, [t])

  const getErrorStats = useCallback((): ErrorStats => {
    const totalErrors = errors.length
    const resolvedErrors = errors.filter(e => e.resolved).length
    const activeErrors = totalErrors - resolvedErrors
    
    const errorsByType = errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const lastError = errors.length > 0 ? errors[0] : null

    return {
      totalErrors,
      resolvedErrors,
      activeErrors,
      errorsByType,
      lastError
    }
  }, [errors])

  const getActiveErrors = useCallback(() => {
    return errors.filter(error => !error.resolved)
  }, [errors])

  const getCriticalErrors = useCallback(() => {
    return errors.filter(error => !error.resolved && error.severity === "critical")
  }, [errors])

  // Specific error handlers for common scenarios
  const handleConnectionError = useCallback((message: string, details?: any) => {
    return logError("connection", message, details, "high")
  }, [logError])

  const handleAudioError = useCallback((message: string, details?: any) => {
    return logError("audio", message, details, "medium")
  }, [logError])

  const handleTranscriptionError = useCallback((message: string, details?: any) => {
    return logError("transcription", message, details, "medium")
  }, [logError])

  const handlePlaybackError = useCallback((message: string, details?: any) => {
    return logError("playback", message, details, "medium")
  }, [logError])

  const handleSessionError = useCallback((message: string, details?: any) => {
    return logError("session", message, details, "high")
  }, [logError])

  const handleCriticalError = useCallback((message: string, details?: any) => {
    return logError("unknown", message, details, "critical")
  }, [logError])

  return {
    errors,
    isRecovering,
    logError,
    resolveError,
    retryError,
    clearErrors,
    getErrorStats,
    getActiveErrors,
    getCriticalErrors,
    getSeverityColor,
    getSeverityIcon,
    
    // Specific handlers
    handleConnectionError,
    handleAudioError,
    handleTranscriptionError,
    handlePlaybackError,
    handleSessionError,
    handleCriticalError
  }
}
