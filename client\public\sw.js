// Audio Agent Service Worker
const CACHE_NAME = 'audio-agent-v1'
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  // Add other static assets as needed
]

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_CACHE_URLS)
      })
      .then(() => {
        console.log('Service Worker installed successfully')
        return self.skipWaiting()
      })
  )
})

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      console.log('Service Worker activated')
      return self.clients.claim()
    })
  )
})

// Fetch event
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // Skip WebSocket connections
  if (url.protocol === 'ws:' || url.protocol === 'wss:') {
    return
  }

  // Skip audio agent backend API calls - always go to network
  if (url.hostname === 'localhost' && url.port === '8000') {
    return
  }

  // Handle API health checks
  if (url.pathname === '/api/health' || url.pathname === '/api/ping') {
    event.respondWith(
      fetch(request).catch(() => {
        return new Response(JSON.stringify({ 
          status: 'offline', 
          timestamp: new Date().toISOString() 
        }), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        })
      })
    )
    return
  }

  // Cache-first strategy for static assets
  if (request.method === 'GET') {
    event.respondWith(
      caches.match(request)
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse
          }

          return fetch(request)
            .then((response) => {
              // Don't cache non-successful responses
              if (!response || response.status !== 200 || response.type !== 'basic') {
                return response
              }

              // Clone the response
              const responseToCache = response.clone()

              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(request, responseToCache)
                })

              return response
            })
            .catch(() => {
              // Return offline page for navigation requests
              if (request.mode === 'navigate') {
                return caches.match('/')
              }
              
              // Return empty response for other requests
              return new Response('Offline', { status: 503 })
            })
        })
    )
  }
})

// Handle background sync for queued messages
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-messages') {
    console.log('Background sync: processing queued messages')
    event.waitUntil(processQueuedMessages())
  }
})

// Handle push notifications (for future use)
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event)
  
  const options = {
    body: event.data ? event.data.text() : 'Audio Agent notification',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    }
  }

  event.waitUntil(
    self.registration.showNotification('Audio Agent', options)
  )
})

// Process queued messages when back online
async function processQueuedMessages() {
  try {
    // This would integrate with the WebSocket manager's message queue
    console.log('Processing queued messages...')
    
    // Send message to main thread to process queue
    const clients = await self.clients.matchAll()
    clients.forEach(client => {
      client.postMessage({
        type: 'PROCESS_MESSAGE_QUEUE'
      })
    })
  } catch (error) {
    console.error('Error processing queued messages:', error)
  }
}

console.log('Audio Agent Service Worker loaded')
