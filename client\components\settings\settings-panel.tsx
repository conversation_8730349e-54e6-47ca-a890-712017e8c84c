"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Settings, Download, Upload, RotateCcw, Check, X } from "lucide-react"
import { useConfiguration } from "@/hooks/use-configuration"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"

interface SettingsPanelProps {
  className?: string
  onClose?: () => void
}

/**
 * Comprehensive settings panel for Audio Agent configuration
 */
export function SettingsPanel({ className = "", onClose }: SettingsPanelProps) {
  const {
    preferences,
    updatePreferences,
    resetPreferences,
    exportSettings,
    importSettings,
    validateConfiguration,
    isDevelopment,
  } = useConfiguration()

  const [activeTab, setActiveTab] = useState("audio")
  const [isResetting, setIsResetting] = useState(false)

  const handleExportSettings = () => {
    try {
      const settings = exportSettings()
      const blob = new Blob([settings], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `audio-agent-settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success("Settings exported successfully")
    } catch (error) {
      toast.error("Failed to export settings")
    }
  }

  const handleImportSettings = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const content = e.target?.result as string
            const success = importSettings(content)
            if (success) {
              toast.success("Settings imported successfully")
            } else {
              toast.error("Invalid settings file")
            }
          } catch (error) {
            toast.error("Failed to import settings")
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  const handleResetSettings = async () => {
    setIsResetting(true)
    try {
      resetPreferences()
      toast.success("Settings reset to defaults")
    } catch (error) {
      toast.error("Failed to reset settings")
    } finally {
      setIsResetting(false)
    }
  }

  return (
    <Card className={`w-full max-w-4xl mx-auto ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>Audio Agent Settings</span>
          </CardTitle>
          <CardDescription>
            Configure your Audio Agent experience
          </CardDescription>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleExportSettings}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={handleImportSettings}>
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleResetSettings}
            disabled={isResetting}
          >
            <RotateCcw className={`w-4 h-4 mr-2 ${isResetting ? 'animate-spin' : ''}`} />
            Reset
          </Button>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="audio">Audio</TabsTrigger>
            <TabsTrigger value="ui">Interface</TabsTrigger>
            <TabsTrigger value="conversation">Conversation</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
            <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
          </TabsList>

          {/* Audio Settings */}
          <TabsContent value="audio" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Audio Configuration</h3>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label>Input Volume</Label>
                    <Slider
                      value={[preferences.audio.inputVolume]}
                      onValueChange={([value]) => 
                        updatePreferences('audio', { inputVolume: value })
                      }
                      max={2}
                      min={0}
                      step={0.1}
                      className="mt-2"
                    />
                    <span className="text-sm text-muted-foreground">
                      {Math.round(preferences.audio.inputVolume * 100)}%
                    </span>
                  </div>

                  <div>
                    <Label>Output Volume</Label>
                    <Slider
                      value={[preferences.audio.outputVolume]}
                      onValueChange={([value]) => 
                        updatePreferences('audio', { outputVolume: value })
                      }
                      max={2}
                      min={0}
                      step={0.1}
                      className="mt-2"
                    />
                    <span className="text-sm text-muted-foreground">
                      {Math.round(preferences.audio.outputVolume * 100)}%
                    </span>
                  </div>

                  <div>
                    <Label>Gain Control</Label>
                    <Slider
                      value={[preferences.audio.gainControl]}
                      onValueChange={([value]) => 
                        updatePreferences('audio', { gainControl: value })
                      }
                      max={2}
                      min={0.1}
                      step={0.1}
                      className="mt-2"
                    />
                    <span className="text-sm text-muted-foreground">
                      {preferences.audio.gainControl.toFixed(1)}x
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Enable Audio Worklet</Label>
                    <Switch
                      checked={preferences.audio.enableAudioWorklet}
                      onCheckedChange={(checked) =>
                        updatePreferences('audio', { enableAudioWorklet: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label>Noise Reduction</Label>
                    <Switch
                      checked={preferences.audio.enableNoiseReduction}
                      onCheckedChange={(checked) =>
                        updatePreferences('audio', { enableNoiseReduction: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label>Echo Cancellation</Label>
                    <Switch
                      checked={preferences.audio.enableEchoCancellation}
                      onCheckedChange={(checked) =>
                        updatePreferences('audio', { enableEchoCancellation: checked })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* UI Settings */}
          <TabsContent value="ui" className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Interface Preferences</h3>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label>Theme</Label>
                    <Select
                      value={preferences.ui.theme}
                      onValueChange={(value: 'light' | 'dark' | 'system') =>
                        updatePreferences('ui', { theme: value })
                      }
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Light</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="system">System</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Language</Label>
                    <Select
                      value={preferences.ui.language}
                      onValueChange={(value: 'en' | 'zh' | 'auto') =>
                        updatePreferences('ui', { language: value })
                      }
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="zh">中文</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Enable Animations</Label>
                    <Switch
                      checked={preferences.ui.enableAnimations}
                      onCheckedChange={(checked) =>
                        updatePreferences('ui', { enableAnimations: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label>Auto Scroll Chat</Label>
                    <Switch
                      checked={preferences.ui.autoScrollChat}
                      onCheckedChange={(checked) =>
                        updatePreferences('ui', { autoScrollChat: checked })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label>Compact Mode</Label>
                    <Switch
                      checked={preferences.ui.compactMode}
                      onCheckedChange={(checked) =>
                        updatePreferences('ui', { compactMode: checked })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Add other tab contents here... */}
          <TabsContent value="conversation">
            <div className="text-center py-8 text-muted-foreground">
              Conversation settings coming soon...
            </div>
          </TabsContent>

          <TabsContent value="performance">
            <div className="text-center py-8 text-muted-foreground">
              Performance settings coming soon...
            </div>
          </TabsContent>

          <TabsContent value="privacy">
            <div className="text-center py-8 text-muted-foreground">
              Privacy settings coming soon...
            </div>
          </TabsContent>

          <TabsContent value="accessibility">
            <div className="text-center py-8 text-muted-foreground">
              Accessibility settings coming soon...
            </div>
          </TabsContent>
        </Tabs>

        {/* Configuration Status */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {validateConfiguration() ? (
                <Check className="w-4 h-4 text-green-600" />
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
              <span className="text-sm font-medium">
                Configuration {validateConfiguration() ? 'Valid' : 'Invalid'}
              </span>
            </div>
            
            {isDevelopment && (
              <span className="text-xs text-muted-foreground">
                Development Mode
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
