import { renderHook, act } from '@testing-library/react'
import { useAudioWorklet } from '../use-audio-worklet'

describe('useAudioWorklet', () => {
  let mockAudioContext: any
  let mockWorkletNode: any

  beforeEach(() => {
    mockWorkletNode = {
      port: {
        postMessage: jest.fn(),
        onmessage: null,
        onmessageerror: null,
      },
      connect: jest.fn(),
      disconnect: jest.fn(),
    }

    mockAudioContext = {
      audioWorklet: {
        addModule: jest.fn().mockResolvedValue(undefined),
      },
      destination: {},
      state: 'running',
    }

    // Mock AudioWorkletNode
    ;(global.AudioWorkletNode as any) = jest.fn(() => mockWorkletNode)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAudioWorklet())

    expect(result.current.isInitialized).toBe(false)
    expect(result.current.isProcessingEnabled).toBe(true)
    expect(result.current.audioStats).toBeNull()
  })

  it('should initialize worklet successfully', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      const workletNode = await result.current.initializeWorklet(mockAudioContext)
      expect(workletNode).toBe(mockWorkletNode)
    })

    expect(mockAudioContext.audioWorklet.addModule).toHaveBeenCalledWith('/audio-worklet-processor.js')
    expect(global.AudioWorkletNode).toHaveBeenCalledWith(
      mockAudioContext,
      'realtime-audio-processor',
      expect.any(Object)
    )
  })

  it('should set gain control', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.setGainControl(1.5)
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'set-gain',
      value: 1.5,
    })
  })

  it('should clamp gain control values', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      result.current.setGainControl(5.0) // Should be clamped to 2.0
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'set-gain',
      value: 2.0,
    })
  })

  it('should set noise gate', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.setNoiseGate(0.05)
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'set-noise-gate',
      value: 0.05,
    })
  })

  it('should set compression ratio', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.setCompressionRatio(3.0)
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'set-compression',
      value: 3.0,
    })
  })

  it('should toggle processing', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.toggleProcessing(false)
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'toggle-processing',
      enabled: false,
    })
    expect(result.current.isProcessingEnabled).toBe(false)
  })

  it('should get stats', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.getStats()
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'get-stats',
    })
  })

  it('should reset stats', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.resetStats()
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.port.postMessage).toHaveBeenCalledWith({
      type: 'reset-stats',
    })
  })

  it('should connect to audio stream', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    const mockSourceNode = { connect: jest.fn() }
    const mockDestinationNode = { connect: jest.fn() }

    await act(async () => {
      const success = result.current.connectToAudioStream(mockSourceNode as any, mockDestinationNode as any)
      expect(success).toBe(true)
    })

    expect(mockSourceNode.connect).toHaveBeenCalledWith(mockWorkletNode)
    expect(mockWorkletNode.connect).toHaveBeenCalledWith(mockDestinationNode)
  })

  it('should disconnect from audio stream', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      const success = result.current.disconnectFromAudioStream()
      expect(success).toBe(true)
    })

    expect(mockWorkletNode.disconnect).toHaveBeenCalled()
  })

  it('should handle worklet initialization failure', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    // Mock addModule to fail
    mockAudioContext.audioWorklet.addModule.mockRejectedValue(new Error('Failed to load worklet'))

    await act(async () => {
      const workletNode = await result.current.initializeWorklet(mockAudioContext)
      expect(workletNode).toBeNull()
    })
  })

  it('should handle message from worklet', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    // Simulate processor ready message
    const readyMessage = { data: { type: 'processor-ready', timestamp: Date.now() } }
    
    await act(async () => {
      if (mockWorkletNode.port.onmessage) {
        mockWorkletNode.port.onmessage(readyMessage)
      }
    })

    expect(result.current.isInitialized).toBe(true)
  })

  it('should cleanup properly', async () => {
    const { result } = renderHook(() => useAudioWorklet())

    await act(async () => {
      await result.current.initializeWorklet(mockAudioContext)
    })

    await act(async () => {
      result.current.cleanup()
    })

    expect(mockWorkletNode.disconnect).toHaveBeenCalled()
    expect(result.current.isInitialized).toBe(false)
  })
})
