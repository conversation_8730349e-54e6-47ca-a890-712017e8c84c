# Audio Agent Configuration Guide

This document describes the comprehensive configuration system for the Audio Agent frontend application.

## Overview

The Audio Agent uses a multi-layered configuration system that combines:

1. **Environment Configuration** - Deployment-specific settings
2. **User Preferences** - Persistent user settings
3. **Runtime Configuration** - Dynamic configuration management

## Environment Configuration

### Configuration Files

- `config/environment.ts` - Main configuration logic
- `.env.local.example` - Example environment variables
- `.env.production` - Production environment settings

### Environment Variables

#### WebSocket Configuration
```bash
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws/conversation
```

#### API Configuration
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### Feature Flags
```bash
NEXT_PUBLIC_ENABLE_DEBUG=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_ENABLE_SERVICE_WORKER=true
NEXT_PUBLIC_ENABLE_PWA=true
```

#### Audio Configuration
```bash
NEXT_PUBLIC_AUDIO_SAMPLE_RATE=24000
NEXT_PUBLIC_AUDIO_CHUNK_SIZE=4096
NEXT_PUBLIC_ENABLE_AUDIO_WORKLET=true
```

### Environment-Specific Overrides

The system supports three environments:

#### Development
- Debug logging enabled
- Performance monitoring enabled
- Debug panel visible
- Console logging enabled

#### Staging
- Remote logging enabled
- Debug panel disabled
- Reduced console logging

#### Production
- Minimal logging
- Performance optimizations
- Error reporting enabled
- Debug features disabled

## User Preferences

### Categories

#### Audio Preferences
- Input/Output volume levels
- Audio processing settings (gain, noise gate, compression)
- Device preferences
- Audio worklet configuration

#### UI Preferences
- Theme selection (light/dark/system)
- Language settings
- Animation preferences
- Chat display options

#### Conversation Preferences
- Default conversation mode
- Transcription settings
- Interruption handling
- Real-time features

#### Performance Preferences
- Monitoring settings
- Debug information
- Logging levels
- Resource limits

#### Privacy Preferences
- Data retention settings
- Analytics preferences
- Error reporting
- Usage data sharing

#### Accessibility Preferences
- High contrast mode
- Reduced motion
- Screen reader support
- Font size adjustments

### Storage

User preferences are stored in `localStorage` with:
- Version management for migrations
- Validation and fallback to defaults
- Export/import functionality
- Real-time synchronization

## Configuration Hooks

### useConfiguration()

Main configuration hook providing:
- Environment configuration access
- User preferences management
- Configuration validation
- Settings export/import

```typescript
const {
  config,
  preferences,
  updatePreferences,
  resetPreferences,
  exportSettings,
  importSettings,
  validateConfiguration
} = useConfiguration()
```

### Specialized Hooks

#### useAudioConfiguration()
```typescript
const {
  inputVolume,
  outputVolume,
  enableAudioWorklet,
  updateAudioConfig
} = useAudioConfiguration()
```

#### useUIConfiguration()
```typescript
const {
  theme,
  enableAnimations,
  autoScrollChat,
  updateUIConfig
} = useUIConfiguration()
```

#### usePerformanceConfiguration()
```typescript
const {
  enablePerformanceMonitoring,
  maxChatHistory,
  updatePerformanceConfig
} = usePerformanceConfiguration()
```

## Settings Panel

The `SettingsPanel` component provides a comprehensive UI for:
- Viewing current configuration
- Modifying user preferences
- Exporting/importing settings
- Resetting to defaults
- Configuration validation

### Usage

```typescript
import { SettingsPanel } from '@/components/settings/settings-panel'

<SettingsPanel onClose={() => setShowSettings(false)} />
```

## Configuration Integration

### WebSocket Manager

The WebSocket manager uses configuration for:
- Connection URLs
- Reconnection parameters
- Heartbeat intervals
- Message queue limits

```typescript
const { config } = useConfiguration()
const wsUrl = config.websocket.url
const maxReconnectAttempts = config.websocket.reconnectAttempts
```

### Audio System

Audio components use configuration for:
- Sample rates and chunk sizes
- Audio worklet settings
- Volume and processing parameters

### UI Components

UI components respect user preferences for:
- Theme and appearance
- Animation settings
- Accessibility options

## Best Practices

### Environment Setup

1. Copy `.env.local.example` to `.env.local`
2. Configure environment-specific values
3. Use appropriate settings for each deployment

### User Preferences

1. Provide sensible defaults
2. Validate user input
3. Handle migration between versions
4. Offer export/import functionality

### Configuration Validation

1. Validate configuration on startup
2. Provide fallbacks for invalid values
3. Log configuration issues
4. Offer reset functionality

### Performance Considerations

1. Cache configuration values
2. Minimize re-renders from config changes
3. Use appropriate update patterns
4. Monitor configuration impact

## Troubleshooting

### Common Issues

#### Configuration Not Loading
- Check environment variable names
- Verify file permissions
- Check browser localStorage

#### Invalid Configuration
- Use validation functions
- Check console for errors
- Reset to defaults if needed

#### Performance Issues
- Review monitoring settings
- Check configuration caching
- Optimize update patterns

### Debug Tools

#### Development Mode
- Configuration logging enabled
- Validation warnings shown
- Debug panel available

#### Settings Panel
- Real-time configuration view
- Validation status display
- Export/import functionality

## Migration Guide

### Version Updates

The system handles preference migrations automatically:
- Version checking on load
- Automatic migration logic
- Fallback to defaults for invalid data

### Adding New Preferences

1. Update `UserPreferences` interface
2. Add to `defaultPreferences`
3. Update settings panel UI
4. Add migration logic if needed

### Environment Changes

1. Update environment configuration
2. Test in all environments
3. Update documentation
4. Deploy with proper settings

## Security Considerations

### Environment Variables
- Use `NEXT_PUBLIC_` prefix for client-side variables
- Keep sensitive data server-side only
- Validate all configuration inputs

### User Preferences
- Sanitize user input
- Validate preference values
- Limit storage size
- Handle malicious data gracefully

### Configuration Validation
- Validate all configuration on load
- Provide secure defaults
- Log security-related issues
- Implement proper error handling
