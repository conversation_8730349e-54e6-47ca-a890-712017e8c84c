"use client"

import { motion } from "framer-motion"
import { FileText, Download } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useChatStore } from "@/store/chat-store"

export function TranscriptStatus() {
  const { messages } = useChatStore()
  const messageCount = messages.length

  const handleDownloadTranscript = () => {
    const transcript = messages
      .map((msg) => `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`)
      .join("\n\n")

    const blob = new Blob([transcript], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `conversation-${new Date().toISOString().split("T")[0]}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <FileText className="w-4 h-4" />
        <span>{messageCount} messages</span>
      </div>

      {messageCount > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          <Button variant="ghost" size="sm" onClick={handleDownloadTranscript} className="h-8 px-2">
            <Download className="w-4 h-4" />
          </Button>
        </motion.div>
      )}
    </div>
  )
}
