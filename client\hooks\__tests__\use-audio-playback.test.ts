import { renderHook, act } from '@testing-library/react'
import { useAudioPlayback } from '../use-audio-playback'

// Mock performance monitor
jest.mock('../use-performance-monitor', () => ({
  usePerformanceMonitor: () => ({
    updateAudioLatency: jest.fn(),
    updateBufferSize: jest.fn(),
  }),
}))

describe('useAudioPlayback', () => {
  let mockAudioContext: any
  let mockBufferSource: any
  let mockGainNode: any

  beforeEach(() => {
    mockBufferSource = {
      connect: jest.fn(),
      start: jest.fn(),
      stop: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      buffer: null,
    }

    mockGainNode = {
      connect: jest.fn(),
      gain: { value: 1 },
    }

    mockAudioContext = {
      createBufferSource: jest.fn(() => mockBufferSource),
      createGain: jest.fn(() => mockGainNode),
      createBuffer: jest.fn(() => ({
        getChannelData: jest.fn(() => new Float32Array(1024)),
        numberOfChannels: 1,
        length: 1024,
        sampleRate: 24000,
      })),
      decodeAudioData: jest.fn().mockResolvedValue({
        getChannelData: jest.fn(() => new Float32Array(1024)),
        numberOfChannels: 1,
        length: 1024,
        sampleRate: 24000,
      }),
      destination: {},
      state: 'running',
      resume: jest.fn().mockResolvedValue(undefined),
    }

    // Mock AudioContext
    ;(global.AudioContext as any) = jest.fn(() => mockAudioContext)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAudioPlayback())

    expect(result.current.isPlaying).toBe(false)
    expect(result.current.volume).toBe(1)
  })

  it('should initialize audio context', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    expect(global.AudioContext).toHaveBeenCalled()
  })

  it('should play audio chunk', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    const base64Data = btoa('test audio data')

    await act(async () => {
      const success = await result.current.playChunk(base64Data, 'test-chunk')
      expect(success).toBe(true)
    })

    expect(mockAudioContext.createBufferSource).toHaveBeenCalled()
    expect(mockBufferSource.start).toHaveBeenCalled()
  })

  it('should handle chunked audio reassembly', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    const chunkData = btoa('chunk data')

    await act(async () => {
      // Send first chunk
      await result.current.handleChunkedAudio('stream1', chunkData, 0, 2, false)
      
      // Send second chunk (complete)
      await result.current.handleChunkedAudio('stream1', chunkData, 1, 2, true)
    })

    expect(mockAudioContext.createBufferSource).toHaveBeenCalled()
  })

  it('should set volume correctly', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    await act(async () => {
      result.current.setVolume(0.5)
    })

    expect(result.current.volume).toBe(0.5)
  })

  it('should handle speech interruption', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    // Start playing audio
    const base64Data = btoa('test audio data')
    await act(async () => {
      await result.current.playChunk(base64Data, 'test-chunk')
    })

    // Interrupt speech
    await act(async () => {
      result.current.handleSpeechInterruption()
    })

    expect(mockBufferSource.stop).toHaveBeenCalled()
  })

  it('should stop all audio', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    // Start playing audio
    const base64Data = btoa('test audio data')
    await act(async () => {
      await result.current.playChunk(base64Data, 'test-chunk')
    })

    // Stop audio
    await act(async () => {
      result.current.stopAudio()
    })

    expect(mockBufferSource.stop).toHaveBeenCalled()
  })

  it('should get playback status', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    const status = result.current.getStatus()

    expect(status).toHaveProperty('isPlaying')
    expect(status).toHaveProperty('queueSize')
    expect(status).toHaveProperty('processedChunksCount')
    expect(status).toHaveProperty('audioContextState')
  })

  it('should handle audio context errors gracefully', async () => {
    // Mock AudioContext to throw error
    ;(global.AudioContext as any) = jest.fn(() => {
      throw new Error('AudioContext not supported')
    })

    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      const context = await result.current.initializeAudioContext()
      expect(context).toBeNull()
    })
  })

  it('should clean up expired chunks', async () => {
    const { result } = renderHook(() => useAudioPlayback())

    await act(async () => {
      await result.current.initializeAudioContext()
    })

    // This should not throw and should clean up properly
    await act(async () => {
      result.current.cleanupExpiredChunks()
    })

    // Test passes if no errors are thrown
    expect(true).toBe(true)
  })
})
