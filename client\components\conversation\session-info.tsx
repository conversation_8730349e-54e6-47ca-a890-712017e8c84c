"use client"

import { useEffect, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Clock, MessageSquare, Headphones, Wifi, WifiOff, AlertTriangle } from "lucide-react"
import { useSessionManager } from "@/hooks/use-session-manager"
import { useWebSocketManager } from "@/hooks/use-websocket-manager"
import { useAppStore } from "@/store/app-store"

export function SessionInfo() {
  // Component disabled - return null to not render anything
  return null

  // Update duration every second
  useEffect(() => {
    if (!currentSession?.isActive) return

    const interval = setInterval(() => {
      setDuration(getSessionDuration())
    }, 1000)

    return () => clearInterval(interval)
  }, [currentSession?.isActive, getSessionDuration])

  if (!currentSession) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700"
      >
        <div className="flex items-center justify-center space-x-2 text-muted-foreground">
          <WifiOff className="w-4 h-4" />
          <span className="text-sm">No active session</span>
        </div>
      </motion.div>
    )
  }

  const getQualityColor = () => {
    switch (currentSession.quality) {
      case "good": return "text-green-500"
      case "poor": return "text-yellow-500"
      case "unstable": return "text-orange-500"
      case "disconnected": return "text-red-500"
      default: return "text-gray-500"
    }
  }

  const getQualityIcon = () => {
    switch (currentSession.quality) {
      case "good": return <Wifi className="w-4 h-4" />
      case "poor": return <Wifi className="w-4 h-4" />
      case "unstable": return <AlertTriangle className="w-4 h-4" />
      case "disconnected": return <WifiOff className="w-4 h-4" />
      default: return <Wifi className="w-4 h-4" />
    }
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="p-4 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border border-blue-200 dark:border-blue-800"
      >
        {/* Session Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <motion.div
              className={`${getQualityColor()}`}
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              {getQualityIcon()}
            </motion.div>
            <span className="text-sm font-medium">
              Session: {currentSession.sessionId.substring(0, 8)}...
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              currentSession.isActive ? "bg-green-500" : "bg-gray-400"
            }`} />
            <span className="text-xs text-muted-foreground capitalize">
              {currentSession.mode}
            </span>
          </div>
        </div>

        {/* Session Stats */}
        <div className="grid grid-cols-3 gap-4">
          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.05 }}
          >
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Clock className="w-3 h-3 text-blue-600 dark:text-blue-400" />
              <span className="text-xs text-blue-600 dark:text-blue-400">Duration</span>
            </div>
            <div className="text-sm font-mono font-medium">
              {formatSessionDuration(duration)}
            </div>
          </motion.div>

          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.05 }}
          >
            <div className="flex items-center justify-center space-x-1 mb-1">
              <MessageSquare className="w-3 h-3 text-purple-600 dark:text-purple-400" />
              <span className="text-xs text-purple-600 dark:text-purple-400">Messages</span>
            </div>
            <div className="text-sm font-mono font-medium">
              {currentSession.messageCount}
            </div>
          </motion.div>

          <motion.div 
            className="text-center"
            whileHover={{ scale: 1.05 }}
          >
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Headphones className="w-3 h-3 text-green-600 dark:text-green-400" />
              <span className="text-xs text-green-600 dark:text-green-400">Audio</span>
            </div>
            <div className="text-sm font-mono font-medium">
              {currentSession.audioChunkCount}
            </div>
          </motion.div>
        </div>

        {/* Connection Quality Indicator */}
        <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Connection Quality:</span>
            <span className={`font-medium capitalize ${getQualityColor()}`}>
              {currentSession.quality}
            </span>
          </div>
          
          {currentSession.connectionAttempts > 1 && (
            <div className="flex items-center justify-between text-xs mt-1">
              <span className="text-muted-foreground">Reconnection Attempts:</span>
              <span className="font-medium text-yellow-600 dark:text-yellow-400">
                {currentSession.connectionAttempts}
              </span>
            </div>
          )}
        </div>

        {/* Session Started Time */}
        <div className="mt-2 text-xs text-muted-foreground text-center">
          Started: {currentSession.startTime.toLocaleTimeString()}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
