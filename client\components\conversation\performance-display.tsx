"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Activity, Cpu, HardDrive, Wifi, Headphones, MessageSquare, ChevronDown, ChevronUp } from "lucide-react"
import { usePerformanceMonitor } from "@/hooks/use-performance-monitor"
import { Button } from "@/components/ui/button"

export function PerformanceDisplay() {
  // Component disabled - return null to not render anything
  return null

  // Update stats periodically
  useEffect(() => {
    if (!isMonitoring) return

    const interval = setInterval(() => {
      setStats(getPerformanceStats())
    }, 3000)

    return () => clearInterval(interval)
  }, [isMonitoring, getPerformanceStats])

  // Auto-start monitoring
  useEffect(() => {
    startMonitoring()
    return () => stopMonitoring()
  }, []) // Remove dependencies to prevent infinite loop

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return "text-green-500"
    if (value <= thresholds.warning) return "text-yellow-500"
    return "text-red-500"
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 B"
    const k = 1024
    const sizes = ["B", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
  }

  const formatLatency = (ms: number) => {
    return `${ms.toFixed(1)}ms`
  }

  if (!isMonitoring) {
    return (
      <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center space-x-2">
          <Activity className="w-4 h-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Performance monitoring disabled</span>
          <Button size="sm" variant="outline" onClick={startMonitoring}>
            Start
          </Button>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 border border-green-200 dark:border-green-800"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
          >
            <Activity className="w-4 h-4 text-green-600 dark:text-green-400" />
          </motion.div>
          <span className="text-sm font-medium">Performance Monitor</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-xs text-muted-foreground">
            {stats.samples} samples
          </span>
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 px-2"
          >
            {isExpanded ? (
              <ChevronUp className="w-3 h-3" />
            ) : (
              <ChevronDown className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Cpu className="w-3 h-3 text-blue-600 dark:text-blue-400" />
            <span className="text-xs text-blue-600 dark:text-blue-400">FPS</span>
          </div>
          <div className={`text-sm font-mono font-medium ${
            getPerformanceColor(60 - metrics.frameRate, { good: 5, warning: 15 })
          }`}>
            {Math.round(metrics.frameRate)}
          </div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <HardDrive className="w-3 h-3 text-purple-600 dark:text-purple-400" />
            <span className="text-xs text-purple-600 dark:text-purple-400">Memory</span>
          </div>
          <div className={`text-sm font-mono font-medium ${
            getPerformanceColor(metrics.memoryUsage, { good: 50, warning: 100 })
          }`}>
            {Math.round(metrics.memoryUsage)}MB
          </div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Headphones className="w-3 h-3 text-green-600 dark:text-green-400" />
            <span className="text-xs text-green-600 dark:text-green-400">Audio</span>
          </div>
          <div className={`text-sm font-mono font-medium ${
            getPerformanceColor(metrics.audioLatency, { good: 50, warning: 100 })
          }`}>
            {formatLatency(metrics.audioLatency)}
          </div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Wifi className="w-3 h-3 text-orange-600 dark:text-orange-400" />
            <span className="text-xs text-orange-600 dark:text-orange-400">Network</span>
          </div>
          <div className={`text-sm font-mono font-medium ${
            getPerformanceColor(metrics.networkLatency, { good: 100, warning: 300 })
          }`}>
            {formatLatency(metrics.networkLatency)}
          </div>
        </div>
      </div>

      {/* Expanded Details */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-3"
          >
            <div className="border-t border-green-200 dark:border-green-800 pt-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Current vs Average */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">Current vs Average</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span>Memory:</span>
                      <span>{Math.round(metrics.memoryUsage)}MB / {Math.round(stats.average.memoryUsage)}MB avg</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Audio Buffer:</span>
                      <span>{formatBytes(metrics.audioBufferSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Message Queue:</span>
                      <span>{metrics.messageQueueSize} items</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CPU Usage:</span>
                      <span className={getPerformanceColor(metrics.cpuUsage, { good: 30, warning: 70 })}>
                        {Math.round(metrics.cpuUsage)}%
                      </span>
                    </div>
                  </div>
                </div>

                {/* Peak Values */}
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2">Peak Values</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span>Max Memory:</span>
                      <span>{Math.round(stats.peak.memoryUsage)}MB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Audio Buffer:</span>
                      <span>{formatBytes(stats.peak.audioBufferSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Network Latency:</span>
                      <span>{formatLatency(stats.peak.networkLatency)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Max Audio Latency:</span>
                      <span>{formatLatency(stats.peak.audioLatency)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Recommendations */}
            <div className="border-t border-green-200 dark:border-green-800 pt-3">
              <h4 className="text-xs font-medium text-muted-foreground mb-2">Recommendations</h4>
              <div className="space-y-1 text-xs text-muted-foreground">
                {metrics.memoryUsage > 100 && (
                  <div>• High memory usage detected - consider refreshing the page</div>
                )}
                {metrics.frameRate < 30 && (
                  <div>• Low frame rate - close other browser tabs for better performance</div>
                )}
                {metrics.audioLatency > 100 && (
                  <div>• High audio latency - check audio device settings</div>
                )}
                {metrics.networkLatency > 300 && (
                  <div>• High network latency - check internet connection</div>
                )}
                {metrics.cpuUsage > 80 && (
                  <div>• High CPU usage - close unnecessary applications</div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
