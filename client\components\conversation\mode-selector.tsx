"use client"

import type React from "react"
import { memo } from "react"
import { motion } from "framer-motion"
import { MessageSquare, UserChe<PERSON> } from "lucide-react"
import { useAppStore } from "@/store/app-store"
import { useWebSocketManager } from "@/hooks/use-websocket-manager"
import type { ConversationMode } from "@/store/app-store"

const ModeCard = memo(
  ({
    mode,
    isActive,
    onClick,
  }: {
    mode: { id: ConversationMode; label: string; icon: React.ReactNode; description: string }
    isActive: boolean
    onClick: () => void
  }) => (
    <motion.button
      onClick={onClick}
      className={`clay-button p-4 text-left transition-all duration-200 relative overflow-hidden group ${
        isActive ? "ring-2 ring-blue-500 dark:ring-blue-400" : ""
      }`}
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      {/* Background gradient for active state */}
      {isActive && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-950/30 dark:to-purple-950/30"
          layoutId="activeMode"
          transition={{ type: "spring", stiffness: 400, damping: 30 }}
        />
      )}

      <div className="flex items-center space-x-3 relative z-10">
        <motion.div
          className={`p-3 rounded-xl transition-all duration-200 ${
            isActive
              ? "bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg"
              : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
          }`}
          whileHover={{ scale: 1.1, rotate: 5 }}
          whileTap={{ scale: 0.9 }}
          animate={isActive ? { scale: [1, 1.05, 1] } : {}}
          transition={
            isActive
              ? { duration: 2, repeat: Number.POSITIVE_INFINITY }
              : { type: "spring", stiffness: 400, damping: 17 }
          }
        >
          {mode.icon}
        </motion.div>
        <div>
          <motion.div
            className="font-semibold text-foreground"
            animate={isActive ? { color: ["#3b82f6", "#8b5cf6", "#3b82f6"] } : {}}
            transition={isActive ? { duration: 3, repeat: Number.POSITIVE_INFINITY } : {}}
          >
            {mode.label}
          </motion.div>
          <div className="text-xs text-muted-foreground mt-1">{mode.description}</div>
        </div>
      </div>

      {/* Enhanced hover effect for entire box */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      />

      {/* Ripple effect on hover */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-2xl"
        initial={{ scale: 0, opacity: 0 }}
        whileHover={{ scale: 1, opacity: [0, 0.5, 0] }}
        transition={{ duration: 0.6 }}
      />
    </motion.button>
  ),
)

ModeCard.displayName = "ModeCard"

export const ModeSelector = memo(() => {
  const { conversationMode, setConversationMode, connectionStatus } = useAppStore()
  const { sendModeSelection, isConnected } = useWebSocketManager()

  const handleModeChange = (mode: ConversationMode) => {
    setConversationMode(mode)

    // Send mode selection to backend if connected
    if (isConnected) {
      sendModeSelection(mode)
      console.log(`🎯 Mode selection sent to backend: ${mode}`)
    } else {
      console.log(`🎯 Mode selection queued (not connected): ${mode}`)
    }
  }

  const modes: { id: ConversationMode; label: string; icon: React.ReactNode; description: string }[] = [
    {
      id: "standard",
      label: "Standard",
      icon: <MessageSquare size={20} />,
      description: "General conversation mode",
    },
    {
      id: "interviewer",
      label: "Interviewer",
      icon: <UserCheck size={20} />,
      description: "Professional interview mode",
    },
  ]

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="font-semibold text-sm text-muted-foreground">
          Select Conversation Mode
          {connectionStatus === "connected" && (
            <span className="ml-2 text-green-500">● Connected</span>
          )}
        </h3>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {modes.map((mode) => (
          <ModeCard
            key={mode.id}
            mode={mode}
            isActive={conversationMode === mode.id}
            onClick={() => handleModeChange(mode.id)}
          />
        ))}
      </div>
    </div>
  )
})

ModeSelector.displayName = "ModeSelector"
