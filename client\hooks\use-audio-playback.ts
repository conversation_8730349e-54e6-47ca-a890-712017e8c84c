"use client"

import { useCallback, useRef, useState, useEffect } from "react"
import { usePerformanceMonitor } from "./use-performance-monitor"
import { DirectAudioPlayer } from "@/utils/direct-audio-player"
import { toast } from "sonner"

// Audio configuration matching the backend - optimized for low latency
const AUDIO_CONFIG = {
  sampleRate: 24000,
  channels: 1,
  format: "pcm16",
  bufferDuration: 0.05, // 50ms buffer for lower latency
}

interface AudioChunk {
  id: string
  data: string // base64 encoded PCM16 data
  timestamp: number
  processed: boolean
  sequence?: number
  isComplete?: boolean
}

interface ChunkedAudioStream {
  streamId: string
  chunks: Map<number, AudioChunk>
  expectedChunks: number
  receivedChunks: number
  isComplete: boolean
  buffer: ArrayBuffer[]
}

/**
 * DirectAudioPlayer equivalent for real-time audio playback
 * Handles PCM16 audio streams with queue management and deduplication
 */
export const useAudioPlayback = () => {
  const { trackAudioBuffer, releaseAudioBuffer, measureAudioLatency } = usePerformanceMonitor()
  const audioContextRef = useRef<AudioContext | null>(null)
  const gainNodeRef = useRef<GainNode | null>(null)
  const directPlayerRef = useRef<DirectAudioPlayer | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(1.0)
  
  // Queue management
  const audioQueueRef = useRef<AudioChunk[]>([])
  const isProcessingQueueRef = useRef(false)
  const nextScheduledTimeRef = useRef(0)

  // Active sources tracking
  const activeSourcesRef = useRef<Set<AudioBufferSourceNode>>(new Set())

  // Deduplication
  const processedChunksRef = useRef<Set<string>>(new Set())
  const maxProcessedChunks = 1000

  // Chunked audio reassembly
  const chunkedStreamsRef = useRef<Map<string, ChunkedAudioStream>>(new Map())
  const maxChunkedStreams = 10
  const audioChunkTimeout = 30000 // 30 seconds like the old frontend
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null)

  const initializeAudioContext = useCallback(async () => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: AUDIO_CONFIG.sampleRate,
      })
    }

    if (audioContextRef.current.state === "suspended") {
      await audioContextRef.current.resume()
    }

    if (!gainNodeRef.current) {
      gainNodeRef.current = audioContextRef.current.createGain()
      gainNodeRef.current.gain.value = volume
      gainNodeRef.current.connect(audioContextRef.current.destination)
    }

    // Initialize DirectAudioPlayer if not already done
    if (!directPlayerRef.current) {
      directPlayerRef.current = new DirectAudioPlayer(AUDIO_CONFIG.sampleRate)
      await directPlayerRef.current.initialize(audioContextRef.current)

      // Set up callbacks
      directPlayerRef.current.onStart = () => {
        setIsPlaying(true)
      }

      directPlayerRef.current.onStop = () => {
        setIsPlaying(false)
      }

      directPlayerRef.current.onError = (error) => {
        console.error("DirectAudioPlayer error:", error)
        toast.error(`Audio playback error: ${error}`)
      }
    }

    nextScheduledTimeRef.current = audioContextRef.current.currentTime

    return audioContextRef.current
  }, [volume])

  const base64ToPCM16 = useCallback((base64Data: string): Int16Array => {
    try {
      // Validate input data
      if (typeof base64Data !== "string" || base64Data.length === 0) {
        throw new Error("Invalid audio data: empty or non-string")
      }

      // Clean and validate base64 data
      let cleanedBase64 = base64Data.trim()

      // Remove any potential data URL prefix
      if (cleanedBase64.includes(',')) {
        cleanedBase64 = cleanedBase64.split(',')[1]
      }

      // Add padding if missing
      while (cleanedBase64.length % 4 !== 0) {
        cleanedBase64 += '='
      }

      // Validate base64 format
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(cleanedBase64)) {
        throw new Error("Invalid base64 format")
      }

      const binaryString = atob(cleanedBase64)

      if (binaryString.length === 0) {
        console.warn("⚠️ Empty audio data after base64 decode")
        return new Int16Array(0)
      }

      // Ensure even length for PCM16 (2 bytes per sample)
      let adjustedBinaryString = binaryString
      if (binaryString.length % 2 !== 0) {
        console.warn("⚠️ Audio data length is not even (PCM16 requires 2 bytes per sample), padding with zero")
        adjustedBinaryString += "\0"
      }

      const bytes = new Uint8Array(adjustedBinaryString.length)
      for (let i = 0; i < adjustedBinaryString.length; i++) {
        bytes[i] = adjustedBinaryString.charCodeAt(i)
      }

      return new Int16Array(bytes.buffer)
    } catch (error) {
      console.error("Failed to decode base64 audio data:", error)
      console.error("Base64 data preview:", base64Data?.substring(0, 100) + "...")
      throw new Error(`Base64 decode failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }, [])

  const pcm16ToFloat32 = useCallback((pcm16Data: Int16Array): Float32Array => {
    if (!pcm16Data || pcm16Data.length === 0) {
      console.warn("⚠️ Empty PCM16 data provided")
      return new Float32Array(0)
    }

    const float32Data = new Float32Array(pcm16Data.length)
    let maxAmplitude = 0

    // Use more efficient conversion method
    for (let i = 0; i < pcm16Data.length; i++) {
      const normalized = pcm16Data[i] / 32768.0
      float32Data[i] = normalized
      maxAmplitude = Math.max(maxAmplitude, Math.abs(normalized))
    }

    // Check if audio is too quiet
    if (maxAmplitude < 0.001) {
      console.debug("🔇 Audio appears to be silent, max amplitude:", maxAmplitude)
    }

    return float32Data
  }, [])

  const createAudioBuffer = useCallback(async (audioData: Float32Array): Promise<AudioBuffer> => {
    const audioContext = await initializeAudioContext()
    const buffer = audioContext.createBuffer(
      AUDIO_CONFIG.channels,
      audioData.length,
      AUDIO_CONFIG.sampleRate
    )
    buffer.copyToChannel(audioData, 0)

    // Track buffer for performance monitoring
    trackAudioBuffer(buffer)

    // Measure audio latency
    measureAudioLatency(audioContext)

    return buffer
  }, [initializeAudioContext, trackAudioBuffer, measureAudioLatency])

  const playAudioBuffer = useCallback(async (buffer: AudioBuffer, when: number = 0): Promise<AudioBufferSourceNode> => {
    const audioContext = await initializeAudioContext()
    const source = audioContext.createBufferSource()
    source.buffer = buffer
    source.connect(gainNodeRef.current!)

    // Track active source
    activeSourcesRef.current.add(source)
    
    source.onended = () => {
      activeSourcesRef.current.delete(source)

      // Release buffer for performance monitoring
      if (source.buffer) {
        releaseAudioBuffer(source.buffer)
      }

      if (activeSourcesRef.current.size === 0) {
        setIsPlaying(false)
      }
    }

    const startTime = when || audioContext.currentTime
    source.start(startTime)
    
    if (!isPlaying) {
      setIsPlaying(true)
    }

    return source
  }, [initializeAudioContext, isPlaying])

  const cleanupExpiredChunks = useCallback(() => {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, stream] of chunkedStreamsRef.current) {
      // Check if any chunk in the stream is expired
      let hasExpiredChunk = false
      for (const chunk of stream.chunks.values()) {
        if (now - chunk.timestamp > audioChunkTimeout) {
          hasExpiredChunk = true
          break
        }
      }

      if (hasExpiredChunk) {
        expiredKeys.push(key)
        console.warn(
          `🗑️ Cleaning up expired audio stream: ${key} (missing ${
            stream.expectedChunks - stream.receivedChunks
          } chunks)`
        )
      }
    }

    // Batch delete expired streams
    expiredKeys.forEach((key) => {
      chunkedStreamsRef.current.delete(key)
    })

    if (expiredKeys.length > 0) {
      console.log(`🧹 Cleaned up ${expiredKeys.length} expired audio streams`)
    }
  }, [audioChunkTimeout])

  const playChunk = useCallback(async (base64Data: string, chunkId?: string): Promise<boolean> => {
    try {
      // Ensure DirectAudioPlayer is initialized
      if (!directPlayerRef.current) {
        await initializeAudioContext()
      }

      if (!directPlayerRef.current) {
        throw new Error("DirectAudioPlayer not initialized")
      }

      // Use DirectAudioPlayer for optimized playback
      await directPlayerRef.current.playChunk(base64Data, chunkId)
      return true
    } catch (error) {
      console.error("❌ Failed to play audio chunk:", error)
      return false
    }
  }, [initializeAudioContext])

  const handleChunkedAudio = useCallback(async (
    streamId: string,
    chunkData: string,
    sequence: number,
    totalChunks: number,
    isComplete: boolean = false
  ): Promise<boolean> => {
    try {
      let stream = chunkedStreamsRef.current.get(streamId)

      if (!stream) {
        stream = {
          streamId,
          chunks: new Map(),
          expectedChunks: totalChunks,
          receivedChunks: 0,
          isComplete: false,
          buffer: []
        }
        chunkedStreamsRef.current.set(streamId, stream)

        // Cleanup old streams
        if (chunkedStreamsRef.current.size > maxChunkedStreams) {
          const oldestStream = Array.from(chunkedStreamsRef.current.keys())[0]
          chunkedStreamsRef.current.delete(oldestStream)
        }
      }

      // Add chunk to stream
      const chunk: AudioChunk = {
        id: `${streamId}_${sequence}`,
        data: chunkData,
        timestamp: Date.now(),
        processed: false,
        sequence,
        isComplete
      }

      stream.chunks.set(sequence, chunk)
      stream.receivedChunks++
      stream.isComplete = isComplete || stream.receivedChunks >= stream.expectedChunks

      console.log(`🧩 Audio chunk ${sequence}/${totalChunks} received for stream ${streamId}`)

      // Check if stream is complete
      if (stream.isComplete) {
        console.log(`🎵 Stream ${streamId} complete, reassembling...`)

        // Reassemble chunks in order
        const sortedChunks = Array.from(stream.chunks.values())
          .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))

        // Combine all chunk data
        let combinedData = ""
        for (const chunk of sortedChunks) {
          combinedData += chunk.data
        }

        // Play reassembled audio
        const success = await playChunk(combinedData, streamId)

        // Cleanup stream
        chunkedStreamsRef.current.delete(streamId)

        return success
      }

      // Trigger cleanup of expired chunks
      cleanupExpiredChunks()

      return true
    } catch (error) {
      console.error("❌ Failed to handle chunked audio:", error)
      return false
    }
  }, [playChunk, cleanupExpiredChunks])



  const stopAudio = useCallback(() => {
    try {
      // Use DirectAudioPlayer interrupt if available
      if (directPlayerRef.current) {
        directPlayerRef.current.interrupt()
      } else {
        // Fallback to manual cleanup
        activeSourcesRef.current.forEach(source => {
          try {
            source.stop()
          } catch (e) {
            // Source might already be stopped
          }
        })

        activeSourcesRef.current.clear()
        setIsPlaying(false)
      }

      console.log("🛑 All audio playback stopped")

    } catch (error) {
      console.error("Failed to stop audio:", error)
    }
  }, [])

  const handleSpeechInterruption = useCallback(() => {
    console.log("🎤 Speech interruption detected - stopping audio playback")
    stopAudio()
  }, [stopAudio])

  const setPlaybackVolume = useCallback((newVolume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, newVolume))
    setVolume(clampedVolume)
    
    if (gainNodeRef.current) {
      gainNodeRef.current.gain.value = clampedVolume
    }
  }, [])

  const getStatus = useCallback(() => {
    const directPlayerStatus = directPlayerRef.current?.getStatus() || {
      isPlaying: false,
      queueLength: 0,
      activeSources: 0
    }

    return {
      isPlaying,
      activeSourcesCount: directPlayerStatus.activeSources,
      queueLength: directPlayerStatus.queueLength,
      processedChunksCount: processedChunksRef.current.size,
      volume,
      audioContextState: audioContextRef.current?.state || "uninitialized",
      directPlayerStatus
    }
  }, [isPlaying, volume])

  // Initialize cleanup interval
  useEffect(() => {
    cleanupIntervalRef.current = setInterval(() => {
      cleanupExpiredChunks()
    }, 10000) // Clean every 10 seconds

    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current)
      }
    }
  }, [cleanupExpiredChunks])

  return {
    playChunk,
    handleChunkedAudio,
    stopAudio,
    handleSpeechInterruption,
    setVolume: setPlaybackVolume,
    getStatus,
    isPlaying,
    volume,
    initializeAudioContext,
    cleanupExpiredChunks
  }
}
