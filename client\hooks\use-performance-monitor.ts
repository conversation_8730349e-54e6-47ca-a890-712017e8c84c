"use client"

import { useCallback, useEffect, useRef, useState } from "react"

export interface PerformanceMetrics {
  memoryUsage: number
  audioBufferSize: number
  messageQueueSize: number
  activeConnections: number
  frameRate: number
  audioLatency: number
  networkLatency: number
  cpuUsage: number
}

export interface PerformanceStats {
  current: PerformanceMetrics
  average: PerformanceMetrics
  peak: PerformanceMetrics
  samples: number
}

/**
 * Performance monitoring and optimization hook
 * Tracks system performance and provides optimization recommendations
 */
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    audioBufferSize: 0,
    messageQueueSize: 0,
    activeConnections: 0,
    frameRate: 60,
    audioLatency: 0,
    networkLatency: 0,
    cpuUsage: 0
  })

  const [isMonitoring, setIsMonitoring] = useState(false)
  const metricsHistoryRef = useRef<PerformanceMetrics[]>([])
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const frameCountRef = useRef(0)
  const lastFrameTimeRef = useRef(performance.now())
  const audioBuffersRef = useRef<Set<AudioBuffer>>(new Set())
  const messageQueuesRef = useRef<any[]>([])

  // Memory usage monitoring
  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      }
    }
    return { used: 0, total: 0, limit: 0 }
  }, [])

  // Frame rate monitoring
  const updateFrameRate = useCallback(() => {
    const now = performance.now()
    const delta = now - lastFrameTimeRef.current
    
    if (delta >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / delta)
      setMetrics(prev => ({ ...prev, frameRate: fps }))
      frameCountRef.current = 0
      lastFrameTimeRef.current = now
    } else {
      frameCountRef.current++
    }

    if (isMonitoring) {
      requestAnimationFrame(updateFrameRate)
    }
  }, [isMonitoring])

  // Audio buffer monitoring
  const trackAudioBuffer = useCallback((buffer: AudioBuffer) => {
    audioBuffersRef.current.add(buffer)
    const totalSize = Array.from(audioBuffersRef.current).reduce((sum, buf) => {
      return sum + (buf.length * buf.numberOfChannels * 4) // 4 bytes per float32 sample
    }, 0)
    
    setMetrics(prev => ({ ...prev, audioBufferSize: totalSize }))
  }, [])

  const releaseAudioBuffer = useCallback((buffer: AudioBuffer) => {
    audioBuffersRef.current.delete(buffer)
    const totalSize = Array.from(audioBuffersRef.current).reduce((sum, buf) => {
      return sum + (buf.length * buf.numberOfChannels * 4)
    }, 0)
    
    setMetrics(prev => ({ ...prev, audioBufferSize: totalSize }))
  }, [])

  // Message queue monitoring
  const updateMessageQueueSize = useCallback((size: number) => {
    setMetrics(prev => ({ ...prev, messageQueueSize: size }))
  }, [])

  // Network latency monitoring
  const measureNetworkLatency = useCallback(async () => {
    const start = performance.now()
    try {
      // Simple ping to measure network latency
      await fetch('/api/ping', { method: 'HEAD' })
      const latency = performance.now() - start
      setMetrics(prev => ({ ...prev, networkLatency: latency }))
    } catch (error) {
      // If ping fails, estimate based on WebSocket response times
      console.warn("Network latency measurement failed:", error)
    }
  }, [])

  // Audio latency monitoring
  const measureAudioLatency = useCallback((audioContext: AudioContext) => {
    if (audioContext) {
      const latency = audioContext.baseLatency + audioContext.outputLatency
      setMetrics(prev => ({ ...prev, audioLatency: latency * 1000 })) // Convert to ms
    }
  }, [])

  // CPU usage estimation (rough approximation)
  const estimateCPUUsage = useCallback(() => {
    const start = performance.now()
    let iterations = 0
    const maxTime = 5 // 5ms test window
    
    while (performance.now() - start < maxTime) {
      iterations++
    }
    
    // Rough estimation based on iterations per ms
    const baselineIterations = 100000 // Calibrated baseline
    const cpuUsage = Math.max(0, Math.min(100, 100 - (iterations / baselineIterations * 100)))
    
    setMetrics(prev => ({ ...prev, cpuUsage }))
  }, [])

  // Collect all metrics
  const collectMetrics = useCallback(() => {
    const memory = getMemoryUsage()
    
    setMetrics(prev => {
      const newMetrics = {
        ...prev,
        memoryUsage: memory.used / (1024 * 1024), // Convert to MB
      }
      
      // Store in history
      metricsHistoryRef.current.push(newMetrics)
      if (metricsHistoryRef.current.length > 100) {
        metricsHistoryRef.current.shift()
      }
      
      return newMetrics
    })

    // Measure network latency periodically
    if (Math.random() < 0.1) { // 10% chance each collection
      measureNetworkLatency()
    }

    // Estimate CPU usage periodically
    if (Math.random() < 0.2) { // 20% chance each collection
      estimateCPUUsage()
    }
  }, [getMemoryUsage, measureNetworkLatency, estimateCPUUsage])

  // Start monitoring
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return

    setIsMonitoring(true)
    
    // Start frame rate monitoring
    requestAnimationFrame(updateFrameRate)
    
    // Start periodic metrics collection
    intervalRef.current = setInterval(collectMetrics, 2000) // Every 2 seconds
    
    console.log("🔍 Performance monitoring started")
  }, [isMonitoring, updateFrameRate, collectMetrics])

  // Stop monitoring
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false)
    
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    
    console.log("🔍 Performance monitoring stopped")
  }, [])

  // Get performance statistics
  const getPerformanceStats = useCallback((): PerformanceStats => {
    const history = metricsHistoryRef.current
    if (history.length === 0) {
      return {
        current: metrics,
        average: metrics,
        peak: metrics,
        samples: 0
      }
    }

    const average: PerformanceMetrics = {
      memoryUsage: history.reduce((sum, m) => sum + m.memoryUsage, 0) / history.length,
      audioBufferSize: history.reduce((sum, m) => sum + m.audioBufferSize, 0) / history.length,
      messageQueueSize: history.reduce((sum, m) => sum + m.messageQueueSize, 0) / history.length,
      activeConnections: history.reduce((sum, m) => sum + m.activeConnections, 0) / history.length,
      frameRate: history.reduce((sum, m) => sum + m.frameRate, 0) / history.length,
      audioLatency: history.reduce((sum, m) => sum + m.audioLatency, 0) / history.length,
      networkLatency: history.reduce((sum, m) => sum + m.networkLatency, 0) / history.length,
      cpuUsage: history.reduce((sum, m) => sum + m.cpuUsage, 0) / history.length,
    }

    const peak: PerformanceMetrics = {
      memoryUsage: Math.max(...history.map(m => m.memoryUsage)),
      audioBufferSize: Math.max(...history.map(m => m.audioBufferSize)),
      messageQueueSize: Math.max(...history.map(m => m.messageQueueSize)),
      activeConnections: Math.max(...history.map(m => m.activeConnections)),
      frameRate: Math.max(...history.map(m => m.frameRate)),
      audioLatency: Math.max(...history.map(m => m.audioLatency)),
      networkLatency: Math.max(...history.map(m => m.networkLatency)),
      cpuUsage: Math.max(...history.map(m => m.cpuUsage)),
    }

    return {
      current: metrics,
      average,
      peak,
      samples: history.length
    }
  }, [metrics])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring()
    }
  }, []) // Remove dependency to prevent infinite loop

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    trackAudioBuffer,
    releaseAudioBuffer,
    updateMessageQueueSize,
    measureAudioLatency,
    getPerformanceStats
  }
}
