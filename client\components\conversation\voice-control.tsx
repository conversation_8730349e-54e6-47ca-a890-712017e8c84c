"use client"

import { BackgroundGradient } from "@/components/ui/background-gradient"
import { ModeSelectorAdvanced } from "@/components/conversation/mode-selector-advanced"
import { ConversationButton } from "@/components/conversation/conversation-button"

import { ConnectionStatusIndicator } from "@/components/conversation/connection-status-indicator"
import { WelcomeMessage } from "@/components/conversation/welcome-message"
import { useTranslations } from 'next-intl'

export function VoiceControl() {
  const t = useTranslations('voiceControl')

  return (
    <BackgroundGradient className="clay-card p-8">
      <div className="space-y-8">
        {/* Header with connection status */}
        <div className="text-center space-y-4">
          <div>
            <h2 className="text-2xl font-bold mb-2">{t('title')}</h2>
            <p className="text-muted-foreground">{t('subtitle')}</p>
          </div>
          <ConnectionStatusIndicator />
        </div>

        {/* Welcome message for new users */}
        <WelcomeMessage />

        {/* Advanced mode selector */}
        <ModeSelectorAdvanced />

        {/* Main conversation controls */}
        <div className="text-center space-y-4">
          <ConversationButton />
          <p className="text-sm text-muted-foreground">{t('instruction')}</p>
        </div>




      </div>
    </BackgroundGradient>
  )
}
