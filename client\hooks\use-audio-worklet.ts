"use client"

import { use<PERSON><PERSON>back, useRef, useState, useEffect } from "react"
import { toast } from "sonner"

interface AudioStats {
  processedSamples: number
  droppedFrames: number
  peakLevel: number
  rmsLevel: number
  processingTime: number
  gainControl: number
  noiseGate: number
  compressionRatio: number
  isEnabled: boolean
  timestamp: number
}

interface AudioWorkletConfig {
  gainControl?: number
  noiseGate?: number
  compressionRatio?: number
  enabled?: boolean
}

/**
 * Advanced AudioWorklet hook for real-time audio processing
 * Provides sophisticated audio enhancement capabilities matching the old frontend
 */
export const useAudioWorklet = () => {
  const audioContextRef = useRef<AudioContext | null>(null)
  const workletNodeRef = useRef<AudioWorkletNode | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isProcessingEnabled, setIsProcessingEnabled] = useState(true)
  const [audioStats, setAudioStats] = useState<AudioStats | null>(null)
  const [config, setConfig] = useState<AudioWorkletConfig>({
    gainControl: 1.0,
    noiseGate: 0.01,
    compressionRatio: 2.0,
    enabled: true
  })

  const initializeWorklet = useCallback(async (audioContext: AudioContext): Promise<AudioWorkletNode | null> => {
    try {
      // Load the audio worklet processor
      await audioContext.audioWorklet.addModule('/audio-worklet.js')
      
      // Create the worklet node
      const workletNode = new AudioWorkletNode(audioContext, 'realtime-audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        channelCount: 1,
        channelCountMode: 'explicit',
        channelInterpretation: 'speakers'
      })

      // Handle messages from the worklet
      workletNode.port.onmessage = (event) => {
        const { type, stats } = event.data
        
        switch (type) {
          case 'processor-ready':
            console.log('🎛️ AudioWorklet processor ready')
            setIsInitialized(true)
            break
            
          case 'audio-stats':
            setAudioStats(stats)
            break
            
          default:
            console.log('AudioWorklet message:', event.data)
        }
      }

      // Handle worklet errors
      workletNode.port.onmessageerror = (error) => {
        console.error('❌ AudioWorklet message error:', error)
        toast.error('Audio processing error occurred')
      }

      workletNodeRef.current = workletNode
      audioContextRef.current = audioContext
      
      console.log('✅ AudioWorklet initialized successfully')
      return workletNode

    } catch (error) {
      console.error('❌ Failed to initialize AudioWorklet:', error)
      toast.error('Failed to initialize audio processing')
      return null
    }
  }, [])

  const setGainControl = useCallback((gain: number) => {
    if (!workletNodeRef.current) return false

    const clampedGain = Math.max(0, Math.min(2.0, gain))
    
    workletNodeRef.current.port.postMessage({
      type: 'set-gain',
      value: clampedGain
    })

    setConfig(prev => ({ ...prev, gainControl: clampedGain }))
    console.log('🎛️ Gain control set to:', clampedGain)
    return true
  }, [])

  const setNoiseGate = useCallback((threshold: number) => {
    if (!workletNodeRef.current) return false

    const clampedThreshold = Math.max(0, Math.min(1.0, threshold))
    
    workletNodeRef.current.port.postMessage({
      type: 'set-noise-gate',
      value: clampedThreshold
    })

    setConfig(prev => ({ ...prev, noiseGate: clampedThreshold }))
    console.log('🎛️ Noise gate set to:', clampedThreshold)
    return true
  }, [])

  const setCompressionRatio = useCallback((ratio: number) => {
    if (!workletNodeRef.current) return false

    const clampedRatio = Math.max(1.0, Math.min(10.0, ratio))
    
    workletNodeRef.current.port.postMessage({
      type: 'set-compression',
      value: clampedRatio
    })

    setConfig(prev => ({ ...prev, compressionRatio: clampedRatio }))
    console.log('🎛️ Compression ratio set to:', clampedRatio)
    return true
  }, [])

  const toggleProcessing = useCallback((enabled: boolean) => {
    if (!workletNodeRef.current) return false

    workletNodeRef.current.port.postMessage({
      type: 'toggle-processing',
      enabled
    })

    setIsProcessingEnabled(enabled)
    setConfig(prev => ({ ...prev, enabled }))
    console.log('🎛️ Audio processing:', enabled ? 'enabled' : 'disabled')
    return true
  }, [])

  const getStats = useCallback(() => {
    if (!workletNodeRef.current) return false

    workletNodeRef.current.port.postMessage({
      type: 'get-stats'
    })
    return true
  }, [])

  const resetStats = useCallback(() => {
    if (!workletNodeRef.current) return false

    workletNodeRef.current.port.postMessage({
      type: 'reset-stats'
    })
    
    setAudioStats(null)
    console.log('🎛️ Audio statistics reset')
    return true
  }, [])

  const connectToAudioStream = useCallback((sourceNode: AudioNode, destinationNode?: AudioNode) => {
    if (!workletNodeRef.current) {
      console.warn('⚠️ AudioWorklet not initialized')
      return false
    }

    try {
      // Connect: source -> worklet -> destination
      sourceNode.connect(workletNodeRef.current)
      
      if (destinationNode) {
        workletNodeRef.current.connect(destinationNode)
      } else if (audioContextRef.current) {
        workletNodeRef.current.connect(audioContextRef.current.destination)
      }

      console.log('🔗 AudioWorklet connected to audio stream')
      return true

    } catch (error) {
      console.error('❌ Failed to connect AudioWorklet:', error)
      return false
    }
  }, [])

  const disconnectFromAudioStream = useCallback(() => {
    if (!workletNodeRef.current) return false

    try {
      workletNodeRef.current.disconnect()
      console.log('🔌 AudioWorklet disconnected from audio stream')
      return true

    } catch (error) {
      console.error('❌ Failed to disconnect AudioWorklet:', error)
      return false
    }
  }, [])

  const cleanup = useCallback(() => {
    if (workletNodeRef.current) {
      try {
        workletNodeRef.current.disconnect()
        workletNodeRef.current = null
      } catch (error) {
        console.error('❌ Error cleaning up AudioWorklet:', error)
      }
    }
    
    audioContextRef.current = null
    setIsInitialized(false)
    setAudioStats(null)
    console.log('🧹 AudioWorklet cleaned up')
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return cleanup
  }, [cleanup])

  return {
    // Initialization
    initializeWorklet,
    isInitialized,
    
    // Audio processing controls
    setGainControl,
    setNoiseGate,
    setCompressionRatio,
    toggleProcessing,
    isProcessingEnabled,
    
    // Statistics and monitoring
    getStats,
    resetStats,
    audioStats,
    
    // Audio stream connection
    connectToAudioStream,
    disconnectFromAudioStream,
    
    // Configuration
    config,
    
    // Cleanup
    cleanup,
    
    // Direct worklet access for advanced use cases
    workletNode: workletNodeRef.current
  }
}
