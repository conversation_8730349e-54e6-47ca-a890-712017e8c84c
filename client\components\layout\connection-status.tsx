"use client"

import { useAppStore } from "@/store/app-store"
import { Wifi, WifiOff, LoaderCircle } from "lucide-react"
import { cn } from "@/lib/utils"

export function ConnectionStatus() {
  const status = useAppStore((state) => state.connectionStatus)

  const statusConfig = {
    connected: {
      icon: <Wifi className="w-4 h-4" />,
      text: "Connected",
      className: "bg-success/10 text-success-foreground border-success/20",
    },
    connecting: {
      icon: <LoaderCircle className="w-4 h-4 animate-spin" />,
      text: "Connecting",
      className: "bg-secondary text-secondary-foreground",
    },
    disconnected: {
      icon: <WifiOff className="w-4 h-4" />,
      text: "Disconnected",
      className: "bg-destructive/10 text-destructive",
    },
    error: {
      icon: <WifiOff className="w-4 h-4" />,
      text: "Error",
      className: "bg-destructive/10 text-destructive",
    },
  }

  const current = statusConfig[status]

  return (
    <div
      className={cn(
        "flex items-center gap-2 px-3 py-1.5 text-xs font-medium rounded-full border transition-colors",
        current.className,
      )}
    >
      {current.icon}
      <span className="hidden sm:inline">{current.text}</span>
    </div>
  )
}
