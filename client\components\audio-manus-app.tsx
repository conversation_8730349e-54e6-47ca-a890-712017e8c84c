"use client"

import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { VoiceControl } from "@/components/conversation/voice-control"
import { ChatInterface } from "@/components/chat/chat-interface"
import { ErrorDisplay } from "@/components/conversation/error-display"
import { DebugPanel } from "@/components/conversation/debug-panel"
import { useAppStore } from "@/store/app-store"
import { useAudioManager } from "@/hooks/use-audio-manager"
import { useWebSocketManager } from "@/hooks/use-websocket-manager"
import { useAudioRecording } from "@/hooks/use-audio-recording"
import { useServiceWorker } from "@/hooks/use-service-worker"

export function AudioManusApp() {
  const { isDarkMode, isConversationActive } = useAppStore()

  // Initialize all the hooks
  useAudioManager()
  useWebSocketManager()
  useServiceWorker()
  const { handleConversationStateChange } = useAudioRecording()

  // Handle conversation state changes
  useEffect(() => {
    handleConversationStateChange()
  }, [isConversationActive, handleConversationStateChange])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <ErrorDisplay />
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <VoiceControl />
            <ChatInterface />
          </div>
        </div>
      </main>
      <DebugPanel />
    </div>
  )
}
