/**
 * AudioWorklet Processor for Real-time Audio Processing
 * Provides advanced audio processing capabilities for Audio Agent
 */

class RealtimeAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();

    // Audio processing parameters
    this.bufferSize = 4096;
    this.inputBuffer = new Float32Array(this.bufferSize);
    this.outputBuffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;

    // Audio enhancement settings
    this.gainControl = 1.0;
    this.noiseGate = 0.01;
    this.compressionRatio = 2.0;
    this.isEnabled = true;

    // Processing statistics
    this.processedSamples = 0;
    this.droppedFrames = 0;

    // Listen for parameter changes from main thread
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    // Send ready signal
    this.port.postMessage({
      type: "processor-ready",
      timestamp: currentTime,
    });
  }

  /**
   * Handle messages from main thread
   * @param {Object} data Message data
   */
  handleMessage(data) {
    switch (data.type) {
      case "set-gain":
        this.gainControl = Math.max(0, Math.min(2.0, data.value));
        break;

      case "set-noise-gate":
        this.noiseGate = Math.max(0, Math.min(1.0, data.value));
        break;

      case "set-compression":
        this.compressionRatio = Math.max(1.0, Math.min(10.0, data.value));
        break;

      case "toggle-processing":
        this.isEnabled = data.enabled;
        break;

      case "get-stats":
        this.sendStats();
        break;

      default:
        console.warn("Unknown message type:", data.type);
    }
  }

  /**
   * Main audio processing method
   * @param {Float32Array[][]} inputs Input audio data
   * @param {Float32Array[][]} outputs Output audio data
   * @param {Object} parameters AudioParam values
   * @returns {boolean} Keep processor alive
   */
  process(inputs, outputs, parameters) {
    const input = inputs[0];
    const output = outputs[0];

    // Ensure we have valid input and output
    if (!input || !output || input.length === 0 || output.length === 0) {
      return true;
    }

    const inputChannel = input[0];
    const outputChannel = output[0];

    if (!inputChannel || !outputChannel) {
      return true;
    }

    try {
      if (this.isEnabled) {
        // Process audio with enhancements
        this.processAudioWithEnhancements(inputChannel, outputChannel);
      } else {
        // Pass through without processing
        outputChannel.set(inputChannel);
      }

      this.processedSamples += inputChannel.length;

      // Send periodic statistics
      if (this.processedSamples % 48000 === 0) {
        // Every second at 48kHz
        this.sendStats();
      }
    } catch (error) {
      // On error, pass through input to output
      outputChannel.set(inputChannel);
      this.droppedFrames++;
    }

    return true;
  }

  /**
   * Process audio with enhancements
   * @param {Float32Array} input Input samples
   * @param {Float32Array} output Output samples
   */
  processAudioWithEnhancements(input, output) {
    for (let i = 0; i < input.length; i++) {
      let sample = input[i];

      // Apply noise gate
      if (Math.abs(sample) < this.noiseGate) {
        sample = 0;
      }

      // Apply compression
      if (Math.abs(sample) > 0.5) {
        const sign = sample >= 0 ? 1 : -1;
        const compressed =
          0.5 + (Math.abs(sample) - 0.5) / this.compressionRatio;
        sample = sign * Math.min(compressed, 1.0);
      }

      // Apply gain control
      sample *= this.gainControl;

      // Ensure output is within valid range
      output[i] = Math.max(-1.0, Math.min(1.0, sample));
    }
  }

  /**
   * Send processing statistics to main thread
   */
  sendStats() {
    this.port.postMessage({
      type: "audio-stats",
      stats: {
        processedSamples: this.processedSamples,
        droppedFrames: this.droppedFrames,
        gainControl: this.gainControl,
        noiseGate: this.noiseGate,
        compressionRatio: this.compressionRatio,
        isEnabled: this.isEnabled,
        timestamp: currentTime,
      },
    });
  }
}

// Register the AudioWorklet processor
registerProcessor("realtime-audio-processor", RealtimeAudioProcessor);

/**
 * AudioWorklet Manager for integrating with AudioManager
 */
class AudioWorkletManager {
  constructor(audioContext) {
    this.audioContext = audioContext;
    this.workletNode = null;
    this.isSupported = this.checkSupport();
    this.isLoaded = false;

    // Event handlers
    this.onStatsUpdate = null;
    this.onError = null;
  }

  /**
   * Check if AudioWorklet is supported
   * @returns {boolean} Support status
   */
  checkSupport() {
    return (
      typeof AudioWorkletNode !== "undefined" &&
      this.audioContext &&
      typeof this.audioContext.audioWorklet !== "undefined"
    );
  }

  /**
   * Load and initialize AudioWorklet
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    if (!this.isSupported) {
      console.warn("AudioWorklet not supported in this browser");
      return false;
    }

    try {
      // Load the worklet module
      const workletUrl = URL.createObjectURL(
        new Blob([document.currentScript.textContent], {
          type: "application/javascript",
        })
      );

      await this.audioContext.audioWorklet.addModule(workletUrl);

      // Create the worklet node
      this.workletNode = new AudioWorkletNode(
        this.audioContext,
        "realtime-audio-processor",
        {
          numberOfInputs: 1,
          numberOfOutputs: 1,
          channelCount: 1,
        }
      );

      // Setup message handling
      this.workletNode.port.onmessage = (event) => {
        this.handleWorkletMessage(event.data);
      };

      this.isLoaded = true;
      console.log("🎛️ AudioWorklet initialized successfully");
      return true;
    } catch (error) {
      console.error("Failed to initialize AudioWorklet:", error);
      this.onError?.(error);
      return false;
    }
  }

  /**
   * Handle messages from AudioWorklet
   * @param {Object} data Message data
   */
  handleWorkletMessage(data) {
    switch (data.type) {
      case "processor-ready":
        console.log("🎛️ AudioWorklet processor ready");
        break;

      case "audio-stats":
        this.onStatsUpdate?.(data.stats);
        break;

      default:
        console.log("AudioWorklet message:", data);
    }
  }

  /**
   * Connect AudioWorklet to audio graph
   * @param {AudioNode} source Source audio node
   * @param {AudioNode} destination Destination audio node
   */
  connect(source, destination) {
    if (!this.workletNode) {
      console.warn("AudioWorklet not initialized");
      return false;
    }

    try {
      source.connect(this.workletNode);
      this.workletNode.connect(destination);
      console.log("🎛️ AudioWorklet connected to audio graph");
      return true;
    } catch (error) {
      console.error("Failed to connect AudioWorklet:", error);
      this.onError?.(error);
      return false;
    }
  }

  /**
   * Set audio processing parameters
   * @param {Object} params Processing parameters
   */
  setParameters(params) {
    if (!this.workletNode) return;

    Object.entries(params).forEach(([key, value]) => {
      this.workletNode.port.postMessage({
        type: `set-${key}`,
        value: value,
      });
    });
  }

  /**
   * Enable or disable audio processing
   * @param {boolean} enabled Processing state
   */
  setEnabled(enabled) {
    if (!this.workletNode) return;

    this.workletNode.port.postMessage({
      type: "toggle-processing",
      enabled: enabled,
    });
  }

  /**
   * Request current statistics
   */
  requestStats() {
    if (!this.workletNode) return;

    this.workletNode.port.postMessage({
      type: "get-stats",
    });
  }

  /**
   * Disconnect and cleanup AudioWorklet
   */
  disconnect() {
    if (this.workletNode) {
      this.workletNode.disconnect();
      this.workletNode = null;
    }
    this.isLoaded = false;
    console.log("🎛️ AudioWorklet disconnected");
  }
}

// Export for use in other modules
if (typeof window !== "undefined") {
  window.AudioWorkletManager = AudioWorkletManager;
}
