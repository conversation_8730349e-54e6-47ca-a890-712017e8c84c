import { create } from "zustand"

export type ConnectionStatus = "connecting" | "connected" | "disconnected" | "error"
export type ConversationMode = "standard" | "interviewer"

interface AppState {
  connectionStatus: ConnectionStatus
  isConversationActive: boolean
  conversationMode: ConversationMode
  audioVolume: number
  hasAudioPermission: boolean | null
  isSettingsOpen: boolean

  // Actions
  setConnectionStatus: (status: ConnectionStatus) => void
  toggleConversation: () => void
  setConversationMode: (mode: ConversationMode) => void
  setAudioVolume: (volume: number) => void
  setHasAudioPermission: (hasPermission: boolean | null) => void
  toggleSettings: () => void
}

export const useAppStore = create<AppState>((set) => ({
  connectionStatus: "connected",
  isConversationActive: false,
  conversationMode: "standard",
  audioVolume: 0,
  hasAudioPermission: null,
  isSettingsOpen: false,

  setConnectionStatus: (status) => set({ connectionStatus: status }),
  toggleConversation: () => set((state) => ({ isConversationActive: !state.isConversationActive })),
  setConversationMode: (mode) => set({ conversationMode: mode }),
  setAudioVolume: (volume) => set({ audioVolume: volume }),
  setHasAudioPermission: (hasPermission) => set({ hasAudioPermission: hasPermission }),
  toggleSettings: () => set((state) => ({ isSettingsOpen: !state.isSettingsOpen })),
}))
