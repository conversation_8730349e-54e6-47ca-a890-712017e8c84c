"use client"

import { motion } from "framer-motion"
import { Mic, Volume2, Activity, Settings } from "lucide-react"
import { useAppStore } from "@/store/app-store"
import { useAudioPlayback } from "@/hooks/use-audio-playback"
import { useAudioWorklet } from "@/hooks/use-audio-worklet"
import { Button } from "@/components/ui/button"

interface AudioInfoPanelProps {
  className?: string
  showControls?: boolean
}

/**
 * Audio information panel matching the old frontend's audio statistics display
 */
export function AudioInfoPanel({ className = "", showControls = true }: AudioInfoPanelProps) {
  // Component disabled - return null to not render anything
  return null

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-foreground flex items-center space-x-2">
          <Activity className="w-5 h-5 text-primary" />
          <span>Audio Information</span>
        </h3>
        {showControls && (
          <Button variant="ghost" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Audio Input Section */}
      <div className="p-4 bg-muted/50 rounded-lg space-y-3">
        <div className="flex items-center space-x-2">
          <Mic className="w-4 h-4 text-green-600" />
          <span className="font-medium text-foreground">Audio Input</span>
          <div className={`w-2 h-2 rounded-full ${hasAudioPermission ? 'bg-green-500' : 'bg-red-500'}`} />
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Volume Level:</span>
            <div className="font-mono text-foreground">
              {formatNumber(audioVolume * 100)}%
            </div>
          </div>
          <div>
            <span className="text-muted-foreground">Permission:</span>
            <div className={`font-medium ${hasAudioPermission ? 'text-green-600' : 'text-red-600'}`}>
              {hasAudioPermission ? 'Granted' : 'Denied'}
            </div>
          </div>
        </div>

        {/* Audio Worklet Stats */}
        {isWorkletInitialized && audioStats && (
          <div className="mt-3 pt-3 border-t border-border">
            <div className="text-xs text-muted-foreground mb-2">Audio Processing</div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Peak Level:</span>
                <div className="font-mono text-foreground">
                  {formatNumber(audioStats.peakLevel * 100)}%
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">RMS Level:</span>
                <div className="font-mono text-foreground">
                  {formatNumber(audioStats.rmsLevel * 100)}%
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Gain:</span>
                <div className="font-mono text-foreground">
                  {formatNumber(audioStats.gainControl)}x
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Noise Gate:</span>
                <div className="font-mono text-foreground">
                  {formatNumber(audioStats.noiseGate * 100)}%
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Audio Output Section */}
      <div className="p-4 bg-muted/50 rounded-lg space-y-3">
        <div className="flex items-center space-x-2">
          <Volume2 className="w-4 h-4 text-blue-600" />
          <span className="font-medium text-foreground">Audio Output</span>
          <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-blue-500' : 'bg-gray-400'}`} />
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground">Volume Level:</span>
            <div className="font-mono text-foreground">
              {formatNumber(playbackVolume * 100)}%
            </div>
          </div>
          <div>
            <span className="text-muted-foreground">Status:</span>
            <div className={`font-medium ${isPlaying ? 'text-blue-600' : 'text-gray-600'}`}>
              {isPlaying ? 'Playing' : 'Idle'}
            </div>
          </div>
          <div>
            <span className="text-muted-foreground">Queue Size:</span>
            <div className="font-mono text-foreground">
              {playbackStatus.queueSize}
            </div>
          </div>
          <div>
            <span className="text-muted-foreground">Processed:</span>
            <div className="font-mono text-foreground">
              {playbackStatus.processedChunksCount}
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground">
          Audio Context: {playbackStatus.audioContextState}
        </div>
      </div>

      {/* Performance Statistics */}
      {audioStats && (
        <div className="p-4 bg-muted/50 rounded-lg space-y-3">
          <div className="flex items-center space-x-2">
            <Activity className="w-4 h-4 text-purple-600" />
            <span className="font-medium text-foreground">Performance</span>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Processed Samples:</span>
              <div className="font-mono text-foreground">
                {audioStats.processedSamples.toLocaleString()}
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Dropped Frames:</span>
              <div className={`font-mono ${audioStats.droppedFrames > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {audioStats.droppedFrames}
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Processing Time:</span>
              <div className="font-mono text-foreground">
                {formatNumber(audioStats.processingTime)}ms
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Compression:</span>
              <div className="font-mono text-foreground">
                {formatNumber(audioStats.compressionRatio)}:1
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">
              Processing: {audioStats.isEnabled ? 'Enabled' : 'Disabled'}
            </span>
            <span className="text-muted-foreground">
              Updated: {new Date(audioStats.timestamp * 1000).toLocaleTimeString()}
            </span>
          </div>
        </div>
      )}

      {/* Configuration Display */}
      {config && (
        <div className="p-3 bg-background/50 rounded border text-xs">
          <div className="text-muted-foreground mb-2">Current Configuration</div>
          <div className="space-y-1 font-mono">
            <div>Gain: {formatNumber(config.gainControl || 1.0)}x</div>
            <div>Noise Gate: {formatNumber((config.noiseGate || 0.01) * 100)}%</div>
            <div>Compression: {formatNumber(config.compressionRatio || 2.0)}:1</div>
            <div>Processing: {config.enabled ? 'On' : 'Off'}</div>
          </div>
        </div>
      )}
    </div>
  )
}
