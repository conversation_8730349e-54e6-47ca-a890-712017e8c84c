import { create } from "zustand"

export interface ChatMessage {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: number
  isStreaming?: boolean
  source?: string // For tracking transcription source (paraformer, qwen, etc.)
  quality?: number // For transcript quality scoring
  messageKey?: string // For deduplication
}

interface ChatState {
  messages: ChatMessage[]
  isAssistantTyping: boolean
  currentStreamingMessageId: string | null
  transcriptStatus: string
  aiStatus: string

  // Actions
  addMessage: (message: Omit<ChatMessage, "id" | "timestamp">) => string
  updateStreamingMessage: (id: string, chunk: string) => void
  replaceStreamingMessage: (id: string, content: string) => void
  completeStreamingMessage: (id: string) => void
  setAssistantTyping: (isTyping: boolean) => void
  clearMessages: () => void

  // New actions for audio agent functionality
  updateTranscriptStatus: (status: string) => void
  updateAIStatus: (status: string) => void
  clearTranscriptStatus: () => void
  clearAIStatus: () => void
  findMessageByKey: (messageKey: string) => ChatMessage | undefined
  removeMessage: (id: string) => void
}

export const useChatStore = create<ChatState>((set, get) => ({
  messages: [],
  isAssistantTyping: false,
  currentStreamingMessageId: null,
  transcriptStatus: "Ready for speech...",
  aiStatus: "",

  addMessage: (message) => {
    // Check for duplicate messages using messageKey
    if (message.messageKey) {
      const existing = get().findMessageByKey(message.messageKey)
      if (existing) {
        console.log("Duplicate message detected, skipping:", message.messageKey)
        return existing.id
      }
    }

    const newMessage: ChatMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
    }

    console.log("🎯 ChatStore addMessage:", {
      id: newMessage.id,
      role: newMessage.role,
      content: newMessage.content.substring(0, 50) + (newMessage.content.length > 50 ? "..." : ""),
      isStreaming: newMessage.isStreaming,
      messageKey: newMessage.messageKey
    })

    set((state) => {
      const newState = {
        messages: [...state.messages, newMessage],
        isAssistantTyping: message.role === "assistant" && message.isStreaming,
        currentStreamingMessageId: message.isStreaming ? newMessage.id : state.currentStreamingMessageId,
      }
      console.log("🎯 ChatStore new state:", {
        messageCount: newState.messages.length,
        isAssistantTyping: newState.isAssistantTyping,
        currentStreamingMessageId: newState.currentStreamingMessageId
      })
      return newState
    })

    return newMessage.id
  },

  updateStreamingMessage: (id, chunk) => {
    set((state) => ({
      messages: state.messages.map((msg) => (msg.id === id ? { ...msg, content: msg.content + chunk } : msg)),
    }))
  },

  replaceStreamingMessage: (id, content) => {
    set((state) => ({
      messages: state.messages.map((msg) => (msg.id === id ? { ...msg, content } : msg)),
    }))
  },

  completeStreamingMessage: (id) => {
    set((state) => ({
      messages: state.messages.map((msg) => (msg.id === id ? { ...msg, isStreaming: false } : msg)),
      isAssistantTyping: false,
      currentStreamingMessageId: state.currentStreamingMessageId === id ? null : state.currentStreamingMessageId,
    }))
  },

  setAssistantTyping: (isTyping) => set({ isAssistantTyping: isTyping }),

  clearMessages: () => set({
    messages: [],
    isAssistantTyping: false,
    currentStreamingMessageId: null
  }),

  // New methods for audio agent functionality
  updateTranscriptStatus: (status) => set({ transcriptStatus: status }),

  updateAIStatus: (status) => set({ aiStatus: status }),

  clearTranscriptStatus: () => set({ transcriptStatus: "Ready for speech..." }),

  clearAIStatus: () => set({ aiStatus: "" }),

  findMessageByKey: (messageKey) => {
    return get().messages.find(msg => msg.messageKey === messageKey)
  },

  removeMessage: (id) => {
    set((state) => ({
      messages: state.messages.filter(msg => msg.id !== id),
      currentStreamingMessageId: state.currentStreamingMessageId === id ? null : state.currentStreamingMessageId,
    }))
  },
}))
