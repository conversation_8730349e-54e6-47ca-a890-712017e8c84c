"use client"

import { motion } from "framer-motion"
import { useAppStore } from "@/store/app-store"

export function MicAnimation() {
  const { audioLevel } = useAppStore()

  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <motion.div
        className="absolute w-full h-full bg-blue-500/20 rounded-full"
        animate={{
          scale: 1 + audioLevel * 0.5,
          opacity: 0.8 - audioLevel * 0.5,
        }}
        transition={{ type: "spring", stiffness: 400, damping: 20 }}
      />
      <motion.div
        className="absolute w-full h-full bg-blue-500/30 rounded-full"
        animate={{
          scale: 1 + audioLevel * 0.2,
          opacity: 1 - audioLevel * 0.2,
        }}
        transition={{ type: "spring", stiffness: 400, damping: 20, delay: 0.1 }}
      />
    </div>
  )
}
