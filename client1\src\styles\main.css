/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette - Enhanced Contrast & Accessibility */
    --primary-color: #00d4aa;
    --primary-color-rgb: 0, 212, 170;
    --primary-dark: #00b894;
    --primary-light: #55efc4;
    --secondary-color: #0984e3;
    --secondary-dark: #0652dd;
    --accent-color: #fdcb6e;
    --accent-dark: #e17055;
    --danger-color: #e84393;
    --danger-dark: #d63031;
    --warning-color: #f39c12;
    --success-color: #00b894;

    /* Sophisticated Dark Theme - Better Depth */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-quaternary: #0e3460;
    --surface: #252a41;
    --surface-hover: #2d3349;
    --surface-elevated: #323751;
    --surface-primary: #ffffff;
    --surface-secondary: #f8fafc;
    --background-primary: #ffffff;

    /* Enhanced Text Hierarchy */
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-accent: #00d4aa;
    --text-inverse: #1a1a2e;

    /* Modern Borders and Shadows */
    --border-color: #374151;
    --border-light: #e5e7eb;
    --border-accent: rgba(0, 212, 170, 0.3);
    --border-radius-sm: 6px;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;

    /* Enhanced Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-glow: 0 0 20px rgba(0, 212, 170, 0.3);

    /* Modern Typography Scale */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    /* Consistent Spacing Scale */
    --spacing-0: 0;
    --spacing-1: 0.25rem;   /* 4px */
    --spacing-2: 0.5rem;    /* 8px */
    --spacing-3: 0.75rem;   /* 12px */
    --spacing-4: 1rem;      /* 16px */
    --spacing-5: 1.25rem;   /* 20px */
    --spacing-6: 1.5rem;    /* 24px */
    --spacing-8: 2rem;      /* 32px */
    --spacing-10: 2.5rem;   /* 40px */
    --spacing-12: 3rem;     /* 48px */
    --spacing-16: 4rem;     /* 64px */

    /* Smooth Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

body {
    font-family: var(--font-family-primary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.7;
    font-size: var(--font-size-base);
    font-weight: 400;
    letter-spacing: -0.01em;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Layout Components */
#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 确保连接状态区域有足够空间 */
.connection-status {
    min-width: 120px;
    display: flex;
    justify-content: flex-end;
}

/* Modern Header */
.header {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-5) 0;
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    gap: var(--spacing-4);
}

.logo h1 {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-1);
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    font-weight: 500;
    opacity: 0.8;
}

/* Enhanced Connection Status */
.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: var(--transition);
    white-space: nowrap;
    min-width: 120px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

.status-indicator::before {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: currentColor;
    animation: statusPulse 2s infinite;
    flex-shrink: 0;
    box-shadow: 0 0 10px currentColor;
}

.status-indicator.connected {
    background: rgba(0, 212, 170, 0.15);
    color: var(--primary-color);
    border: 1px solid rgba(0, 212, 170, 0.4);
    box-shadow: var(--shadow-sm), 0 0 20px rgba(0, 212, 170, 0.2);
}

.status-indicator.connecting {
    background: rgba(253, 203, 110, 0.15);
    color: var(--accent-color);
    border: 1px solid rgba(253, 203, 110, 0.4);
}

.status-indicator.disconnected {
    background: rgba(232, 67, 147, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(232, 67, 147, 0.4);
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.1);
    }
}

/* Enhanced Main Content Layout */
.main-content {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: var(--spacing-8);
    grid-template-areas:
        "controls audio"
        "conversation conversation";
    min-height: 0;
}

.control-panel {
    grid-area: controls;
    background: rgba(37, 42, 65, 0.8);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-10);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    min-height: 0;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.control-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.5;
}

.audio-section {
    grid-area: audio;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-5);
    min-height: 0;
}

.conversation-section {
    grid-area: conversation;
    background: rgba(37, 42, 65, 0.8);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-8);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    min-height: 400px;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.conversation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--secondary-color), transparent);
    opacity: 0.5;
}

.stats-panel {
    grid-area: stats;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "controls"
            "audio"
            "conversation";
        gap: var(--spacing-4);
    }

    .control-panel,
    .audio-section,
    .conversation-section {
        padding: var(--spacing-4);
    }
}

@media (max-width: 768px) {
    /* 修复header在小屏幕的显示 */
    .header-content {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: center;
        text-align: center;
    }
    
    .connection-status {
        justify-content: center;
        min-width: auto;
        width: 100%;
    }
    
    .status-indicator {
        justify-content: center;
        width: 100%;
        max-width: 200px;
    }
    
    /* 修复主内容区域 */
    .main-content {
        padding: var(--spacing-4);
        gap: var(--spacing-3);
    }
    
    /* 修复模式按钮 */
    .mode-buttons {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .mode-btn {
        flex: none;
        min-width: auto;
        width: 100%;
    }
    
    /* 修复音频设置 */
    .setting-group {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }
    
    .setting-group label {
        min-width: auto;
        text-align: left;
    }
    
    /* 修复统计面板 */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-2);
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-1);
    }
    
    .stat-label {
        margin-right: 0;
    }
    
    /* 修复对话区域 */
    .conversation-section {
        min-height: 300px;
        max-height: 450px;
    }
    
    .chat-container {
        max-height: 400px;
        min-height: 200px;
    }
    
    .chat-message {
        max-width: 95%;
    }
    
    /* 修复footer */
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-2);
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
    }
}

/* Enhanced Panel Headers */
h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-6);
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    letter-spacing: -0.01em;
    position: relative;
}

h3::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, var(--border-color), transparent);
    margin-left: var(--spacing-4);
}

/* 对话控制区域 */
.conversation-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4);
}

.main-control {
    display: flex;
    justify-content: center;
}

/* Enhanced Conversation Button */
.conversation-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-primary);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8) var(--spacing-10);
    cursor: pointer;
    transition: var(--transition-slow);
    font-size: var(--font-size-lg);
    font-weight: 700;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-3);
    min-width: 240px;
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    position: relative;
    overflow: hidden;
    letter-spacing: -0.01em;
}

.conversation-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.conversation-btn:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl), 0 20px 40px -10px rgba(0, 212, 170, 0.4);
}

.conversation-btn:hover::before {
    left: 100%;
}

.conversation-btn:active {
    transform: translateY(-2px) scale(1.01);
}

.conversation-btn .btn-icon {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-1);
}

.conversation-btn .btn-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.conversation-btn .btn-status {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    font-weight: 400;
}

/* 不同状态的按钮样式 */
.conversation-btn[data-state="mode-required"] {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    cursor: not-allowed;
    opacity: 0.7;
}

.conversation-btn[data-state="mode-required"]:hover {
    transform: none;
    box-shadow: var(--shadow-lg);
}

.conversation-btn[data-state="disconnected"] {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.conversation-btn[data-state="connecting"] {
    background: linear-gradient(135deg, var(--warning-color), #ea580c);
    animation: pulse-warning 2s infinite;
}

.conversation-btn[data-state="connected"] {
    background: linear-gradient(135deg, var(--secondary-color), #2563eb);
}

.conversation-btn[data-state="recording"] {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    animation: recording-pulse 1.5s infinite;
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes recording-pulse {
    0%, 100% {
        box-shadow: 0 15px 25px -5px rgba(239, 68, 68, 0.3);
    }
    50% {
        box-shadow: 0 15px 25px -5px rgba(239, 68, 68, 0.6);
    }
}

/* Enhanced Mode Selection */
.mode-selection {
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-8);
    width: 100%;
    max-width: 600px;
    animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: var(--spacing-8);
    background: rgba(37, 42, 65, 0.6);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.mode-selection h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-xl);
    font-weight: 700;
    text-align: center;
    letter-spacing: -0.01em;
}

.mode-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-5);
}

.mode-btn {
    background: rgba(37, 42, 65, 0.8);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    cursor: pointer;
    transition: var(--transition-slow);
    font-size: var(--font-size-sm);
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-3);
    text-align: center;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.mode-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mode-btn:hover {
    background: rgba(45, 51, 73, 0.9);
    border-color: var(--primary-color);
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 10px 20px rgba(0, 212, 170, 0.2);
}

.mode-btn:hover::before {
    opacity: 1;
}

.mode-btn.active {
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.15), rgba(9, 132, 227, 0.1));
    color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg), 0 8px 25px rgba(0, 212, 170, 0.3);
    transform: scale(1.02);
}

.mode-btn .mode-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-1);
}

.mode-btn .mode-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
}

.mode-btn .mode-desc {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    line-height: 1.4;
}

/* Control Buttons */
.control-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
}

/* Modern Button System */
.primary-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-slow);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    letter-spacing: -0.01em;
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.primary-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 10px 20px rgba(0, 212, 170, 0.3);
}

.primary-btn:hover:not(:disabled)::before {
    left: 100%;
}

.primary-btn:disabled {
    background: var(--surface);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.secondary-btn {
    background: rgba(37, 42, 65, 0.8);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    padding: var(--spacing-4) var(--spacing-6);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-slow);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.secondary-btn:hover:not(:disabled) {
    background: rgba(45, 51, 73, 0.9);
    border-color: var(--danger-color);
    color: var(--danger-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), 0 8px 16px rgba(232, 67, 147, 0.2);
}

.secondary-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
}

/* Audio Settings */
.audio-settings {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

/* 修复音频设置布局 */
.setting-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    flex-wrap: nowrap; /* 防止设置项换行 */
}

.setting-group label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    min-width: 80px;
    white-space: nowrap; /* 防止标签文字换行 */
}

.setting-group input[type="range"] {
    flex: 1;
    accent-color: var(--primary-color);
}

.setting-group select {
    flex: 1;
    background: var(--surface);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

#volume-value {
    min-width: 30px;
    font-size: var(--font-size-sm);
    color: var(--text-accent);
    font-weight: 500;
}

/* Enhanced Audio Panels */
.audio-panel {
    background: rgba(37, 42, 65, 0.8);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.audio-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    opacity: 0.6;
}

.waveform {
    height: 80px;
    background: var(--surface);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-3);
    position: relative;
    overflow: hidden;
}

.waveform::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

.audio-info {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Volume Meter */
.volume-meter {
    width: 100%;
    height: 20px;
    background: var(--surface);
    border-radius: var(--border-radius);
    overflow: hidden;
    position: relative;
    border: 1px solid var(--border-color);
}

.volume-bar {
    height: 100%;
    width: 0%;
    background: var(--primary-color);
    transition: width 0.1s ease-out;
    border-radius: var(--border-radius);
}

/* Audio Visualizer */
.audio-visualizer {
    width: 100%;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.audio-visualizer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.audio-visualizer canvas {
    border-radius: 8px;
    background: transparent;
}

/* Conversation Display */
.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.clear-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: var(--transition);
}

.clear-btn:hover {
    background: #dc2626;
}

/* 对话显示修复 */
.conversation-display {
    flex: 1;
    max-height: none; /* 修复：移除固定高度 */
    overflow-y: auto;
    background: var(--surface);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
    min-height: 0; /* 修复：允许收缩 */
}

.welcome-message {
    text-align: center;
    color: var(--text-muted);
    padding: var(--spacing-8);
}

.message {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-3);
    border-radius: var(--border-radius);
    position: relative;
    word-wrap: break-word;
}

.message.user {
    background: rgba(59, 130, 246, 0.1);
    border-left: 3px solid var(--secondary-color);
    margin-right: var(--spacing-6);
}

.message.assistant {
    background: rgba(16, 185, 129, 0.1);
    border-left: 3px solid var(--primary-color);
    margin-left: var(--spacing-6);
}

.message.system {
    background: rgba(156, 163, 175, 0.1);
    border-left: 3px solid #9ca3af;
    margin: 0 var(--spacing-3);
    font-style: italic;
    text-align: center;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-role {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.message-time {
    font-size: var(--font-size-xs);
}

.message-content {
    color: var(--text-primary);
    width: auto;
}

/* Statistics Panel */
/* 修复统计面板布局 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-4);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    background: var(--surface);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    flex-wrap: nowrap; /* 修复：防止统计项内容换行 */
    min-width: 0; /* 修复：允许收缩 */
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-right: var(--spacing-2);
    white-space: nowrap; /* 修复：防止标签换行 */
    overflow: hidden;
    text-overflow: ellipsis; /* 修复：长文本省略 */
}

.stat-item span:last-child {
    font-weight: 600;
    color: var(--text-accent);
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-4) 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.footer-links {
    display: flex;
    gap: var(--spacing-4);
}

.footer-links a {
    color: var(--text-muted);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    transition: var(--transition);
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-6);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--surface);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--surface-hover);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message {
    animation: slideIn 0.3s ease-out;
}

/* Loading States */
.loading {
    position: relative;
    color: transparent !important;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Modern Utility Classes */
.hidden {
    display: none !important;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Text Utilities */
.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-primary {
    color: var(--text-primary) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

/* Background Utilities */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-surface {
    background-color: var(--surface) !important;
}

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }

/* Flexbox Utilities */
.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-center { justify-content: center !important; }
.justify-between { justify-content: space-between !important; }
.align-center { align-items: center !important; }
.align-start { align-items: flex-start !important; }
.flex-1 { flex: 1 !important; }

/* Border Radius Utilities */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

/* Enhanced Chat Interface Styles */

/* Chat Container */
.chat-container {
    grid-area: conversation;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: var(--spacing-6);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    min-height: 500px;
    max-height: 600px;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-3);
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-input-area {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-3);
}

/* Enhanced Chat Messages */
.chat-message {
  margin-bottom: var(--spacing-6);
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 80%;
  clear: both;
  position: relative;
}

.chat-message.message-user {
  margin-left: auto;
  margin-right: 0;
  float: right;
}

.chat-message.message-assistant {
  margin-left: 0;
  margin-right: auto;
  float: left;
}

.chat-message.message-system {
  margin: 0 auto var(--spacing-6) auto;
  max-width: 75%;
  text-align: center;
  float: none;
}

.message-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.message-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 12px;
}

.message.user .message-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.message.assistant .message-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.message.system .message-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.message-sender {
    font-weight: 500;
    color: var(--text-primary);
}

.message-time {
    margin-left: auto;
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* Enhanced Message Bodies */
.message-body {
    background: rgba(37, 42, 65, 0.9);
    backdrop-filter: blur(15px);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-5);
    position: relative;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.chat-message.message-user .message-body {
    background: linear-gradient(135deg, rgba(9, 132, 227, 0.2), rgba(9, 132, 227, 0.1));
    border: 1px solid rgba(9, 132, 227, 0.4);
    box-shadow: var(--shadow-sm), 0 4px 12px rgba(9, 132, 227, 0.15);
}

.chat-message.message-assistant .message-body {
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(0, 212, 170, 0.1));
    border: 1px solid rgba(0, 212, 170, 0.4);
    box-shadow: var(--shadow-sm), 0 4px 12px rgba(0, 212, 170, 0.15);
}

.chat-message.message-system .message-body {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.2), rgba(156, 163, 175, 0.1));
    border: 1px solid rgba(156, 163, 175, 0.4);
    font-style: italic;
    text-align: center;
}

.message-content {
    color: var(--text-primary);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message.streaming .message-content {
    border-color: #f97316;
    background: rgba(249, 115, 22, 0.1);
    position: relative;
}

/* 🎯 移除旧的流式指示器，使用新的光标效果 */

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 12px;
    margin-left: var(--spacing-2);
}

.status-indicator.typing {
    background: rgba(249, 158, 11, 0.2);
    color: var(--accent-color);
}

.status-indicator.streaming {
    background: rgba(16, 185, 129, 0.2);
    color: var(--primary-color);
    animation: pulse 1.5s infinite;
}

.status-indicator.playing {
    background: rgba(139, 69, 19, 0.2);
    color: #8b4513;
    animation: pulse 1s infinite;
}

.status-indicator.error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger-color);
}

/* Typing Animation */
.typing-animation {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-2) 0;
}

.typing-animation span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) { animation-delay: -0.32s; }
.typing-animation span:nth-child(2) { animation-delay: -0.16s; }

.streaming-placeholder {
    color: var(--text-muted);
    font-style: italic;
    padding: var(--spacing-1) 0;
}

/* 🎯 新增：流式文本效果样式 */
.streaming-text {
    display: inline;
    transition: opacity 0.2s ease-in-out;
}

.streaming-text-container {
    display: inline;
}

.message-streaming .message-content {
    position: relative;
}

/* 🎯 流式文本光标样式 */
.streaming-cursor {
    display: inline;
    color: var(--primary-color);
    font-weight: bold;
    margin-left: 2px;
    font-size: 1em;
    line-height: 1;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 🎯 增强流式消息的视觉效果 */
.message-streaming .message-content {
    border-left: 3px solid var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.05);
    animation: streamingGlow 2s ease-in-out infinite alternate;
}

@keyframes streamingGlow {
    0% { box-shadow: 0 0 5px rgba(var(--primary-color-rgb), 0.3); }
    100% { box-shadow: 0 0 15px rgba(var(--primary-color-rgb), 0.6); }
}

/* 🎯 转录状态容器样式 */
.transcript-status-container {
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4);
    background: var(--surface-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2);
    background: var(--surface-primary);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.status-label {
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 100px;
    flex-shrink: 0;
}

.status-text {
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    flex: 1;
    padding: var(--spacing-1) var(--spacing-2);
    background: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 4px;
    border: 1px solid rgba(var(--primary-color-rgb), 0.1);
    transition: all 0.2s ease;
}

.status-text.active {
    background: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
}

/* 🎯 改进：流式光标样式增强 */
.streaming-cursor {
    display: inline;
    color: var(--primary-color);
    font-weight: bold;
    margin-left: 2px;
    font-size: 1em;
    line-height: 1;
    animation: blink 1s infinite;
}

/* 🎯 新增：转录状态容器标题样式 */
.transcript-status-container h4 {
    margin: 0 0 var(--spacing-2) 0;
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* 🎯 错误容器样式 */
.error-container {
    margin: var(--spacing-4) 0;
    padding: 0;
}

.error-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #dc2626;
    margin-bottom: var(--spacing-2);
}

.error-icon {
    font-size: 1.2em;
    flex-shrink: 0;
}

.error-text {
    flex: 1;
    font-weight: 500;
}

.error-dismiss {
    background: none;
    border: none;
    color: #dc2626;
    font-size: 1.5em;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.error-dismiss:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Audio Controls */
.audio-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-2);
    padding-top: var(--spacing-2);
    border-top: 1px solid var(--border-color);
}

.audio-play-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.audio-play-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.audio-duration {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Current Status Area */
.current-status {
    background: var(--surface);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    font-size: var(--font-size-sm);
}

.status-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
}

.status-icon {
    font-size: var(--font-size-base);
}

.status-text {
    color: var(--text-secondary);
    flex: 1;
}

/* System Message Styling */
.system-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
}

.system-message .message-icon {
    font-size: var(--font-size-2xl);
}

.system-message .message-content {
    text-align: center;
}

.system-message .message-content p {
    margin-bottom: var(--spacing-2);
}

.system-message .message-content p:last-child {
    margin-bottom: 0;
}

/* Message State Styling */
.message-typing .message-body {
    border: 2px dashed rgba(249, 158, 11, 0.5);
    animation: breathe 2s infinite;
}

.message-streaming .message-body {
    border: 2px dashed rgba(16, 185, 129, 0.5);
    animation: breathe 2s infinite;
}

.message-playing .message-body {
    border: 2px solid rgba(139, 69, 19, 0.5);
    box-shadow: 0 0 10px rgba(139, 69, 19, 0.3);
}

.message-error .message-body {
    border: 2px solid rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.1);
}

/* Enhanced Animations */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes breathe {
    0%, 100% {
        border-color: rgba(16, 185, 129, 0.3);
    }
    50% {
        border-color: rgba(16, 185, 129, 0.7);
    }
}

/* Responsive Chat Design */
@media (max-width: 768px) {
    .chat-container {
        max-height: 400px;
        min-height: 300px;
    }
    
    .chat-message {
        max-width: 95%;
    }
    
    .message-body {
        padding: var(--spacing-2);
    }
    
    .message-header {
        font-size: var(--font-size-xs);
    }
}

/* Scroll styling for chat messages */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--surface);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
} 

/* 音频可视化改进 */
.waveform {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9em;
    position: relative;
    overflow: hidden;
}

.waveform.active {
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.2), rgba(249, 115, 22, 0.2));
}

.waveform.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: wave 2s infinite;
}

@keyframes wave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 音量计改进 */
.volume-meter {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
    position: relative;
}

.volume-meter .volume-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #f97316, #ef4444);
    border-radius: 4px;
    transition: width 0.1s ease-out;
    position: relative;
}

.volume-meter .volume-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 音频信息显示改进 */
.audio-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 0.9em;
    color: var(--text-secondary);
}

.audio-info span {
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    backdrop-filter: blur(4px);
}

/* 实时状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
}

.status-indicator.connected {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-indicator.connecting {
    background: #f97316;
    animation: pulse 1.5s infinite;
}

.status-indicator.disconnected {
    background: #ef4444;
}

.status-indicator.connected::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 1px solid #10b981;
    border-radius: 50%;
    animation: ripple 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
} 

/* 添加响应式修复 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
    }
    
    .connection-status {
        justify-content: center;
        min-width: auto;
    }
    
    .mode-buttons {
        flex-direction: column;
    }
    
    .mode-btn {
        min-width: auto;
    }
}

/* 🎯 新增：合作型附和样式 */
#aiStatus.interjection {
    color: #667eea !important; /* 使用紫蓝色来区别于普通状态 */
    font-style: italic;
    font-weight: 500;
    transition: opacity 0.3s ease-in-out;
    background: rgba(102, 126, 234, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    border-left: 3px solid #667eea;
} 

/* Add any additional application styles here */

/* --- 新增：合作型附和样式 --- */
.cooperative-interjection {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 20px 32px;
  border-radius: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.2);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1000;
  pointer-events: none;
  backdrop-filter: blur(10px);
  text-align: center;
  min-width: 200px;
  max-width: 400px;
  word-wrap: break-word;
}

.cooperative-interjection.visible {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  animation: pulse 2s ease-in-out;
}

/* 添加脉冲动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.4);
  }
  50% {
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.8), 0 0 0 10px rgba(76, 175, 80, 0.1);
  }
  100% {
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.4);
  }
}



/* 🎯 第三种方案：临时消息样式 */
.temporary-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 152, 0, 0.95);
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 1.1em;
  font-weight: 500;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  max-width: 80%;
  text-align: center;
}

.temporary-message.visible {
  opacity: 1;
}

/* 🎯 第三种方案：录音按钮禁用状态 */
.record-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #6c757d !important;
}

.record-button.disabled:hover {
  background-color: #6c757d !important;
  transform: none !important;
}