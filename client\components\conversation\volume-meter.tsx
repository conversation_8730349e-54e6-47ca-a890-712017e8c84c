"use client"

import { motion } from "framer-motion"
import { useAppStore } from "@/store/app-store"
import { useAudioPlayback } from "@/hooks/use-audio-playback"

interface VolumeMeterProps {
  className?: string
  showLabels?: boolean
}

/**
 * Volume meter component matching the old frontend's volume visualization
 */
export function VolumeMeter({ className = "", showLabels = true }: VolumeMeterProps) {
  const { audioVolume } = useAppStore()
  const { volume: playbackVolume, isPlaying } = useAudioPlayback()

  // Normalize volumes to 0-1 range
  const inputLevel = Math.min(audioVolume * 2, 1)
  const outputLevel = Math.min(playbackVolume, 1)

  const getVolumeColor = (level: number) => {
    if (level < 0.3) return "bg-green-500"
    if (level < 0.7) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getVolumeText = (level: number) => {
    if (level === 0) return "Silent"
    if (level < 0.2) return "Very Low"
    if (level < 0.4) return "Low"
    if (level < 0.6) return "Medium"
    if (level < 0.8) return "High"
    return "Very High"
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Input Volume Meter */}
      <div className="space-y-2">
        {showLabels && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">
              🎤 Input Volume
            </span>
            <span className="text-xs text-muted-foreground">
              {getVolumeText(inputLevel)} ({Math.round(inputLevel * 100)}%)
            </span>
          </div>
        )}
        
        <div className="relative h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          {/* Background segments */}
          <div className="absolute inset-0 flex">
            {Array.from({ length: 20 }, (_, i) => (
              <div
                key={i}
                className="flex-1 border-r border-gray-300 dark:border-gray-600 last:border-r-0"
              />
            ))}
          </div>
          
          {/* Volume bar */}
          <motion.div
            className={`h-full ${getVolumeColor(inputLevel)} transition-colors duration-200`}
            initial={{ width: 0 }}
            animate={{ width: `${inputLevel * 100}%` }}
            transition={{ duration: 0.1 }}
          />
          
          {/* Peak indicator */}
          {inputLevel > 0 && (
            <motion.div
              className="absolute top-0 w-0.5 h-full bg-white shadow-sm"
              initial={{ left: 0 }}
              animate={{ left: `${inputLevel * 100}%` }}
              transition={{ duration: 0.1 }}
            />
          )}
        </div>
      </div>

      {/* Output Volume Meter */}
      <div className="space-y-2">
        {showLabels && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-muted-foreground">
              🔊 Output Volume
            </span>
            <div className="flex items-center space-x-2">
              {isPlaying && (
                <motion.div
                  className="w-2 h-2 bg-blue-500 rounded-full"
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 0.8, repeat: Infinity }}
                />
              )}
              <span className="text-xs text-muted-foreground">
                {getVolumeText(outputLevel)} ({Math.round(outputLevel * 100)}%)
              </span>
            </div>
          </div>
        )}
        
        <div className="relative h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          {/* Background segments */}
          <div className="absolute inset-0 flex">
            {Array.from({ length: 20 }, (_, i) => (
              <div
                key={i}
                className="flex-1 border-r border-gray-300 dark:border-gray-600 last:border-r-0"
              />
            ))}
          </div>
          
          {/* Volume bar */}
          <motion.div
            className={`h-full ${getVolumeColor(outputLevel)} transition-colors duration-200`}
            initial={{ width: 0 }}
            animate={{ width: `${outputLevel * 100}%` }}
            transition={{ duration: 0.1 }}
          />
          
          {/* Peak indicator */}
          {outputLevel > 0 && (
            <motion.div
              className="absolute top-0 w-0.5 h-full bg-white shadow-sm"
              initial={{ left: 0 }}
              animate={{ left: `${outputLevel * 100}%` }}
              transition={{ duration: 0.1 }}
            />
          )}
        </div>
      </div>

      {/* Combined Status */}
      <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${inputLevel > 0 ? 'bg-green-500' : 'bg-gray-400'}`} />
          <span>Input {inputLevel > 0 ? 'Active' : 'Inactive'}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-blue-500' : 'bg-gray-400'}`} />
          <span>Output {isPlaying ? 'Active' : 'Inactive'}</span>
        </div>
      </div>
    </div>
  )
}
