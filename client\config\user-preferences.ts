/**
 * User preferences management for Audio Agent
 * Handles persistent user settings and preferences
 */

export interface UserPreferences {
  // Audio Preferences
  audio: {
    inputVolume: number
    outputVolume: number
    enableNoiseReduction: boolean
    enableEchoCancellation: boolean
    preferredInputDevice?: string
    preferredOutputDevice?: string
    enableAudioWorklet: boolean
    gainControl: number
    noiseGate: number
    compressionRatio: number
  }

  // UI Preferences
  ui: {
    theme: 'light' | 'dark' | 'system'
    language: 'en' | 'zh' | 'auto'
    enableAnimations: boolean
    showWelcomeMessage: boolean
    autoScrollChat: boolean
    chatFontSize: 'small' | 'medium' | 'large'
    enableKeyboardShortcuts: boolean
    compactMode: boolean
  }

  // Conversation Preferences
  conversation: {
    defaultMode: 'standard' | 'interviewer'
    autoStartRecording: boolean
    enableInterruptions: boolean
    transcriptionSource: 'paraformer' | 'qwen' | 'auto'
    showTranscriptionConfidence: boolean
    enableRealTimeTranscription: boolean
  }

  // Performance Preferences
  performance: {
    enablePerformanceMonitoring: boolean
    showDebugInfo: boolean
    enableAdvancedLogging: boolean
    maxChatHistory: number
    enableServiceWorker: boolean
    enableOfflineMode: boolean
  }

  // Privacy Preferences
  privacy: {
    saveConversationHistory: boolean
    enableAnalytics: boolean
    shareUsageData: boolean
    enableErrorReporting: boolean
    dataRetentionDays: number
  }

  // Accessibility Preferences
  accessibility: {
    enableHighContrast: boolean
    enableReducedMotion: boolean
    enableScreenReader: boolean
    fontSize: number
    enableVoiceAnnouncements: boolean
  }
}

// Default user preferences
const defaultPreferences: UserPreferences = {
  audio: {
    inputVolume: 1.0,
    outputVolume: 1.0,
    enableNoiseReduction: true,
    enableEchoCancellation: true,
    enableAudioWorklet: true,
    gainControl: 1.0,
    noiseGate: 0.01,
    compressionRatio: 2.0,
  },

  ui: {
    theme: 'system',
    language: 'auto',
    enableAnimations: true,
    showWelcomeMessage: true,
    autoScrollChat: true,
    chatFontSize: 'medium',
    enableKeyboardShortcuts: true,
    compactMode: false,
  },

  conversation: {
    defaultMode: 'standard',
    autoStartRecording: false,
    enableInterruptions: true,
    transcriptionSource: 'auto',
    showTranscriptionConfidence: false,
    enableRealTimeTranscription: true,
  },

  performance: {
    enablePerformanceMonitoring: true,
    showDebugInfo: false,
    enableAdvancedLogging: false,
    maxChatHistory: 100,
    enableServiceWorker: true,
    enableOfflineMode: true,
  },

  privacy: {
    saveConversationHistory: true,
    enableAnalytics: false,
    shareUsageData: false,
    enableErrorReporting: true,
    dataRetentionDays: 30,
  },

  accessibility: {
    enableHighContrast: false,
    enableReducedMotion: false,
    enableScreenReader: false,
    fontSize: 16,
    enableVoiceAnnouncements: false,
  },
}

// Storage key for preferences
const PREFERENCES_STORAGE_KEY = 'audio-agent-preferences'
const PREFERENCES_VERSION = '1.0.0'

// Preferences manager class
class UserPreferencesManager {
  private preferences: UserPreferences
  private listeners: Set<(preferences: UserPreferences) => void> = new Set()

  constructor() {
    this.preferences = this.loadPreferences()
  }

  // Load preferences from localStorage
  private loadPreferences(): UserPreferences {
    if (typeof window === 'undefined') {
      return defaultPreferences
    }

    try {
      const stored = localStorage.getItem(PREFERENCES_STORAGE_KEY)
      if (!stored) {
        return defaultPreferences
      }

      const parsed = JSON.parse(stored)
      
      // Check version compatibility
      if (parsed.version !== PREFERENCES_VERSION) {
        console.log('🔄 Migrating user preferences to new version')
        return this.migratePreferences(parsed.preferences || parsed)
      }

      return this.validateAndMergePreferences(parsed.preferences || parsed)
    } catch (error) {
      console.error('❌ Failed to load user preferences:', error)
      return defaultPreferences
    }
  }

  // Save preferences to localStorage
  private savePreferences(): void {
    if (typeof window === 'undefined') return

    try {
      const toSave = {
        version: PREFERENCES_VERSION,
        preferences: this.preferences,
        lastUpdated: new Date().toISOString(),
      }

      localStorage.setItem(PREFERENCES_STORAGE_KEY, JSON.stringify(toSave))
      console.log('💾 User preferences saved')
    } catch (error) {
      console.error('❌ Failed to save user preferences:', error)
    }
  }

  // Validate and merge preferences with defaults
  private validateAndMergePreferences(stored: any): UserPreferences {
    const merged = { ...defaultPreferences }

    // Deep merge stored preferences with defaults
    for (const category in stored) {
      if (merged[category as keyof UserPreferences]) {
        merged[category as keyof UserPreferences] = {
          ...merged[category as keyof UserPreferences],
          ...stored[category],
        }
      }
    }

    return merged
  }

  // Migrate preferences from older versions
  private migratePreferences(oldPreferences: any): UserPreferences {
    // Add migration logic here for future versions
    return this.validateAndMergePreferences(oldPreferences)
  }

  // Get all preferences
  getPreferences(): UserPreferences {
    return { ...this.preferences }
  }

  // Get preferences for a specific category
  getCategory<K extends keyof UserPreferences>(category: K): UserPreferences[K] {
    return { ...this.preferences[category] }
  }

  // Update preferences for a specific category
  updateCategory<K extends keyof UserPreferences>(
    category: K,
    updates: Partial<UserPreferences[K]>
  ): void {
    this.preferences[category] = {
      ...this.preferences[category],
      ...updates,
    }

    this.savePreferences()
    this.notifyListeners()
  }

  // Update a specific preference
  updatePreference<K extends keyof UserPreferences, P extends keyof UserPreferences[K]>(
    category: K,
    preference: P,
    value: UserPreferences[K][P]
  ): void {
    this.preferences[category][preference] = value
    this.savePreferences()
    this.notifyListeners()
  }

  // Reset preferences to defaults
  resetPreferences(): void {
    this.preferences = { ...defaultPreferences }
    this.savePreferences()
    this.notifyListeners()
  }

  // Reset a specific category to defaults
  resetCategory<K extends keyof UserPreferences>(category: K): void {
    this.preferences[category] = { ...defaultPreferences[category] }
    this.savePreferences()
    this.notifyListeners()
  }

  // Subscribe to preference changes
  subscribe(listener: (preferences: UserPreferences) => void): () => void {
    this.listeners.add(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener)
    }
  }

  // Notify all listeners of changes
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getPreferences())
      } catch (error) {
        console.error('❌ Error in preference listener:', error)
      }
    })
  }

  // Export preferences as JSON
  exportPreferences(): string {
    return JSON.stringify({
      version: PREFERENCES_VERSION,
      preferences: this.preferences,
      exportedAt: new Date().toISOString(),
    }, null, 2)
  }

  // Import preferences from JSON
  importPreferences(json: string): boolean {
    try {
      const imported = JSON.parse(json)
      
      if (imported.preferences) {
        this.preferences = this.validateAndMergePreferences(imported.preferences)
        this.savePreferences()
        this.notifyListeners()
        return true
      }
      
      return false
    } catch (error) {
      console.error('❌ Failed to import preferences:', error)
      return false
    }
  }
}

// Create singleton instance
export const userPreferences = new UserPreferencesManager()

// Export default preferences for reference
export { defaultPreferences }

// Utility functions
export const getPreference = <K extends keyof UserPreferences, P extends keyof UserPreferences[K]>(
  category: K,
  preference: P
): UserPreferences[K][P] => {
  return userPreferences.getCategory(category)[preference]
}

export const setPreference = <K extends keyof UserPreferences, P extends keyof UserPreferences[K]>(
  category: K,
  preference: P,
  value: UserPreferences[K][P]
): void => {
  userPreferences.updatePreference(category, preference, value)
}
