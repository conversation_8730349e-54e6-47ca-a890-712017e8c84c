/**
 * Configuration management hook for Audio Agent
 * Provides access to environment config and user preferences
 */

import { useState, useEffect, useCallback } from 'react'
import { config, AudioAgentConfig, isDevelopment } from '@/config/environment'
import { userPreferences, UserPreferences } from '@/config/user-preferences'

interface ConfigurationHook {
  // Environment configuration
  config: AudioAgentConfig
  isDevelopment: boolean
  isProduction: boolean
  
  // User preferences
  preferences: UserPreferences
  updatePreferences: <K extends keyof UserPreferences>(
    category: K,
    updates: Partial<UserPreferences[K]>
  ) => void
  resetPreferences: () => void
  
  // Specific preference getters
  getAudioConfig: () => UserPreferences['audio'] & AudioAgentConfig['audio']
  getUIConfig: () => UserPreferences['ui'] & AudioAgentConfig['ui']
  getPerformanceConfig: () => UserPreferences['performance'] & AudioAgentConfig['performance']
  
  // Configuration utilities
  exportSettings: () => string
  importSettings: (json: string) => boolean
  validateConfiguration: () => boolean
}

/**
 * Hook for managing application configuration and user preferences
 */
export function useConfiguration(): ConfigurationHook {
  const [preferences, setPreferences] = useState<UserPreferences>(
    userPreferences.getPreferences()
  )

  // Subscribe to preference changes
  useEffect(() => {
    const unsubscribe = userPreferences.subscribe((newPreferences) => {
      setPreferences(newPreferences)
    })

    return unsubscribe
  }, [])

  // Update preferences
  const updatePreferences = useCallback(<K extends keyof UserPreferences>(
    category: K,
    updates: Partial<UserPreferences[K]>
  ) => {
    userPreferences.updateCategory(category, updates)
  }, [])

  // Reset preferences
  const resetPreferences = useCallback(() => {
    userPreferences.resetPreferences()
  }, [])

  // Get merged audio configuration
  const getAudioConfig = useCallback(() => {
    return {
      ...config.audio,
      ...preferences.audio,
    }
  }, [preferences.audio])

  // Get merged UI configuration
  const getUIConfig = useCallback(() => {
    return {
      ...config.ui,
      ...preferences.ui,
    }
  }, [preferences.ui])

  // Get merged performance configuration
  const getPerformanceConfig = useCallback(() => {
    return {
      ...config.performance,
      ...preferences.performance,
    }
  }, [preferences.performance])

  // Export all settings
  const exportSettings = useCallback(() => {
    const settings = {
      environment: process.env.NODE_ENV,
      config: config,
      preferences: preferences,
      exportedAt: new Date().toISOString(),
      version: '1.0.0',
    }

    return JSON.stringify(settings, null, 2)
  }, [preferences])

  // Import settings
  const importSettings = useCallback((json: string) => {
    try {
      const settings = JSON.parse(json)
      
      if (settings.preferences) {
        return userPreferences.importPreferences(JSON.stringify({
          preferences: settings.preferences
        }))
      }
      
      return false
    } catch (error) {
      console.error('❌ Failed to import settings:', error)
      return false
    }
  }, [])

  // Validate current configuration
  const validateConfiguration = useCallback(() => {
    try {
      // Validate environment config
      if (!config.websocket.url) return false
      if (!config.api.baseUrl) return false
      
      // Validate audio preferences
      const audioConfig = getAudioConfig()
      if (audioConfig.inputVolume < 0 || audioConfig.inputVolume > 2) return false
      if (audioConfig.outputVolume < 0 || audioConfig.outputVolume > 2) return false
      if (audioConfig.gainControl < 0.1 || audioConfig.gainControl > 2.0) return false
      
      // Validate UI preferences
      const uiConfig = getUIConfig()
      if (!['light', 'dark', 'system'].includes(uiConfig.theme)) return false
      
      return true
    } catch (error) {
      console.error('❌ Configuration validation failed:', error)
      return false
    }
  }, [getAudioConfig, getUIConfig])

  return {
    config,
    isDevelopment: isDevelopment(),
    isProduction: !isDevelopment(),
    preferences,
    updatePreferences,
    resetPreferences,
    getAudioConfig,
    getUIConfig,
    getPerformanceConfig,
    exportSettings,
    importSettings,
    validateConfiguration,
  }
}

/**
 * Hook for specific audio configuration
 */
export function useAudioConfiguration() {
  const { getAudioConfig, updatePreferences } = useConfiguration()
  const audioConfig = getAudioConfig()

  const updateAudioConfig = useCallback((updates: Partial<UserPreferences['audio']>) => {
    updatePreferences('audio', updates)
  }, [updatePreferences])

  return {
    ...audioConfig,
    updateAudioConfig,
  }
}

/**
 * Hook for specific UI configuration
 */
export function useUIConfiguration() {
  const { getUIConfig, updatePreferences } = useConfiguration()
  const uiConfig = getUIConfig()

  const updateUIConfig = useCallback((updates: Partial<UserPreferences['ui']>) => {
    updatePreferences('ui', updates)
  }, [updatePreferences])

  return {
    ...uiConfig,
    updateUIConfig,
  }
}

/**
 * Hook for specific performance configuration
 */
export function usePerformanceConfiguration() {
  const { getPerformanceConfig, updatePreferences } = useConfiguration()
  const performanceConfig = getPerformanceConfig()

  const updatePerformanceConfig = useCallback((updates: Partial<UserPreferences['performance']>) => {
    updatePreferences('performance', updates)
  }, [updatePreferences])

  return {
    ...performanceConfig,
    updatePerformanceConfig,
  }
}

/**
 * Hook for conversation configuration
 */
export function useConversationConfiguration() {
  const { preferences, updatePreferences } = useConfiguration()
  const conversationConfig = preferences.conversation

  const updateConversationConfig = useCallback((updates: Partial<UserPreferences['conversation']>) => {
    updatePreferences('conversation', updates)
  }, [updatePreferences])

  return {
    ...conversationConfig,
    updateConversationConfig,
  }
}

/**
 * Hook for privacy configuration
 */
export function usePrivacyConfiguration() {
  const { preferences, updatePreferences } = useConfiguration()
  const privacyConfig = preferences.privacy

  const updatePrivacyConfig = useCallback((updates: Partial<UserPreferences['privacy']>) => {
    updatePreferences('privacy', updates)
  }, [updatePreferences])

  return {
    ...privacyConfig,
    updatePrivacyConfig,
  }
}

/**
 * Hook for accessibility configuration
 */
export function useAccessibilityConfiguration() {
  const { preferences, updatePreferences } = useConfiguration()
  const accessibilityConfig = preferences.accessibility

  const updateAccessibilityConfig = useCallback((updates: Partial<UserPreferences['accessibility']>) => {
    updatePreferences('accessibility', updates)
  }, [updatePreferences])

  return {
    ...accessibilityConfig,
    updateAccessibilityConfig,
  }
}
