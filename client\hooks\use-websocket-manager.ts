"use client"

import { useCallback, useEffect, useRef } from "react"
import { useAppStore } from "@/store/app-store"
import { useChatStore } from "@/store/chat-store"
import { useAudioPlayback } from "./use-audio-playback"
import { useSessionManager } from "./use-session-manager"
import { useErrorHand<PERSON> } from "./use-error-handler"
import { usePerformanceMonitor } from "./use-performance-monitor"
import { useTranscriptionBuffer } from "./use-transcription-buffer"
import { useConfiguration } from "./use-configuration"
import { toast } from "sonner"

// WebSocket message types from the backend
export interface WebSocketMessage {
  type: string
  [key: string]: any
}

export function useWebSocketManager() {
  const { setConnectionStatus, conversationMode } = useAppStore()
  const {
    addMessage,
    updateStreamingMessage,
    replaceStreamingMessage,
    completeStreamingMessage,
    updateTranscriptStatus,
    updateAIStatus,
    clearTranscriptStatus,
    clearAIStatus,
    currentStreamingMessageId
  } = useChatStore()
  const { playChunk, handleChunkedAudio, handleSpeechInterruption, initializeAudioContext } = useAudioPlayback()
  const {
    currentSession,
    createSession,
    endSession,
    incrementMessageCount,
    incrementAudioChunkCount,
    updateConnectionQuality: updateSessionConnectionQuality,
    incrementConnectionAttempts
  } = useSessionManager()
  const {
    handleConnectionError,
    handleAudioError,
    handleTranscriptionError,
    handlePlaybackError,
    resolveError,
    retryError
  } = useErrorHandler()
  const { updateMessageQueueSize } = usePerformanceMonitor()
  const {
    handleTranscriptComplete,
    handleTranscriptDelta,
    clearAllBuffers,
    transcriptStatus: bufferTranscriptStatus
  } = useTranscriptionBuffer()
  const { config } = useConfiguration()

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const sessionIdRef = useRef<string | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const messageQueueRef = useRef<WebSocketMessage[]>([])
  const streamingResponseRef = useRef<string>("")
  const currentStreamingIdRef = useRef<string | null>(null)

  // Configuration-based values
  const maxReconnectAttempts = config.websocket.reconnectAttempts
  const reconnectDelay = config.websocket.reconnectDelay
  const heartbeatInterval = config.websocket.heartbeatInterval

  // Heartbeat/ping system
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastPingTimeRef = useRef<number>(0)
  const connectionQualityRef = useRef<"good" | "poor" | "unstable" | "error">("good")

  const generateSessionId = useCallback(() => {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }, [])

  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        const pingTime = Date.now()
        lastPingTimeRef.current = pingTime

        // Send ping directly via WebSocket
        wsRef.current.send(JSON.stringify({
          type: "ping",
          timestamp: new Date().toISOString(),
          session_id: sessionIdRef.current
        }))

        console.log("📡 Heartbeat ping sent")
      }
    }, heartbeatInterval) // Use configured heartbeat interval
  }, [])

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
      heartbeatIntervalRef.current = null
    }
  }, [])

  const updateConnectionQuality = useCallback((quality: "good" | "poor" | "unstable" | "error") => {
    connectionQualityRef.current = quality
    // Map error to disconnected for session manager
    const sessionQuality = quality === "error" ? "disconnected" : quality
    updateSessionConnectionQuality(sessionQuality)
    console.log("📊 Connection quality updated:", quality)
  }, [updateSessionConnectionQuality])

  const connect = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) return

    setConnectionStatus("connecting")

    // Generate session ID if not exists
    if (!sessionIdRef.current) {
      sessionIdRef.current = generateSessionId()
    }

    // Connect to the actual audio agent backend using configuration
    const wsUrl = `${config.websocket.url}?session_id=${sessionIdRef.current}`
    wsRef.current = new WebSocket(wsUrl)

    wsRef.current.onopen = () => {
      console.log("🔗 WebSocket connected to audio agent backend")
      setConnectionStatus("connected")
      reconnectAttemptsRef.current = 0

      // Create or update session
      if (sessionIdRef.current) {
        if (!currentSession || currentSession.sessionId !== sessionIdRef.current) {
          createSession(sessionIdRef.current, conversationMode)
        }
        updateConnectionQuality("good")
      }

      // Start heartbeat system
      startHeartbeat()

      toast.success("Connected to Audio Agent")

      // Process any queued messages
      processMessageQueue()
    }

    wsRef.current.onmessage = (event) => {
      try {
        if (typeof event.data === "string") {
          const message: WebSocketMessage = JSON.parse(event.data)
          console.log("📥 Received message:", message.type, message)
          handleBackendMessage(message)
        } else {
          console.warn("Received non-string WebSocket message, ignoring")
        }
      } catch (e) {
        console.error("Failed to parse WebSocket message:", e)
        handleConnectionError("Failed to parse server message", {
          error: e,
          rawData: event.data,
          sessionId: sessionIdRef.current
        })
      }
    }

    wsRef.current.onclose = (event) => {
      console.log("❌ WebSocket disconnected:", event.code, event.reason)
      setConnectionStatus("disconnected")
      updateConnectionQuality("error")

      // Stop heartbeat
      stopHeartbeat()

      // End current session if it exists
      if (currentSession && currentSession.isActive) {
        endSession(event.code === 1000 ? "normal closure" : "connection lost")
      }

      if (event.code !== 1000) {
        // Not normal closure - attempt reconnection
        attemptReconnect()
      } else {
        toast.info("Connection closed")
      }
    }

    wsRef.current.onerror = (error) => {
      console.error("🚨 WebSocket error:", error)
      setConnectionStatus("error")
      updateConnectionQuality("error")

      const errorId = handleConnectionError("WebSocket connection error", {
        error,
        sessionId: sessionIdRef.current,
        timestamp: new Date().toISOString()
      })

      // Provide retry mechanism
      setTimeout(() => {
        retryError(errorId, async () => {
          try {
            connect()
            return true
          } catch (retryErr) {
            console.error("Retry failed:", retryErr)
            return false
          }
        })
      }, 2000)
    }
  }, [
    setConnectionStatus,
    addMessage,
    updateStreamingMessage,
    completeStreamingMessage,
    generateSessionId,
    updateTranscriptStatus,
    updateAIStatus,
    clearTranscriptStatus,
    clearAIStatus,
    currentStreamingMessageId,
    playChunk,
    handleSpeechInterruption,
    initializeAudioContext,
    conversationMode,
    currentSession,
    createSession,
    updateConnectionQuality
  ])

  const attemptReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
      toast.error("Failed to reconnect after multiple attempts")
      updateConnectionQuality("error")
      return
    }

    reconnectAttemptsRef.current++
    incrementConnectionAttempts()
    updateConnectionQuality("poor")

    const delay = Math.min(reconnectDelay * Math.pow(2, reconnectAttemptsRef.current - 1), 30000)

    toast.warning(`Connection lost. Reconnecting in ${delay/1000}s... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`)

    reconnectTimeoutRef.current = setTimeout(() => {
      connect()
    }, delay)
  }, [connect, maxReconnectAttempts, incrementConnectionAttempts, updateConnectionQuality])

  const processMessageQueue = useCallback(() => {
    if (messageQueueRef.current.length > 0 && wsRef.current?.readyState === WebSocket.OPEN) {
      console.log(`📤 Processing ${messageQueueRef.current.length} queued messages`)
      messageQueueRef.current.forEach(message => {
        wsRef.current?.send(JSON.stringify(message))
      })
      messageQueueRef.current = []
      updateMessageQueueSize(0)
    }
  }, [updateMessageQueueSize])

  const handleBackendMessage = useCallback((message: WebSocketMessage) => {
    console.log("🎯 Processing message:", message.type, "data:", message)
    try {
      switch (message.type) {
      case "transcript_delta":
        // Handle streaming transcription with buffer system
        console.log("📝 Transcript delta:", message.text, "source:", message.source)
        if (message.text && message.source) {
          handleTranscriptDelta(message.source, message.text)
        }
        break

      case "transcript_complete":
        // Handle completed transcription with sophisticated buffer coordination
        console.log("✅ Transcript complete:", message.text, "source:", message.source)
        if (message.text && message.source) {
          handleTranscriptComplete(
            message.source,
            message.text,
            message.quality || 0.8,
            message.message_key
          )
          incrementMessageCount()
        }
        clearTranscriptStatus()
        break

      case "ai_response":
      case "ai_transcript":
        // Handle AI response (streaming or complete) - both ai_response and ai_transcript
        console.log("🤖 AI response:", message.text, "complete:", message.complete, "type:", message.type)

        if (!message.text && message.complete) {
          // Empty completion message - just clear AI status
          clearAIStatus()
          if (currentStreamingIdRef.current) {
            completeStreamingMessage(currentStreamingIdRef.current)
            currentStreamingIdRef.current = null
          }
          break
        }

        if (message.text) {
          console.log("🎯 AI Response Debug:", {
            text: message.text,
            complete: message.complete,
            messageKey: message.message_key,
            responseId: message.response_id,
            currentStreamingId: currentStreamingIdRef.current,
            storeStreamingId: currentStreamingMessageId,
            hasCurrentStreaming: !!currentStreamingIdRef.current,
            streamingResponseLength: streamingResponseRef.current.length
          })

          // Check if this is a continuation of existing streaming response
          // by checking if we have a current streaming message and the message is not complete
          const shouldContinueStreaming = currentStreamingIdRef.current && !message.complete

          if (!shouldContinueStreaming) {
            // Start new streaming response or handle complete message
            console.log("🎯 Starting new streaming message or handling complete message")
            updateAIStatus("AI is responding...")

            if (currentStreamingIdRef.current && message.complete) {
              // This is a completion of existing streaming message
              streamingResponseRef.current += message.text
              replaceStreamingMessage(currentStreamingIdRef.current, streamingResponseRef.current)
              completeStreamingMessage(currentStreamingIdRef.current)
              clearAIStatus()
              incrementMessageCount()
              streamingResponseRef.current = ""
              currentStreamingIdRef.current = null
              console.log("🎯 Completed existing streaming message")
            } else {
              // This is a new message
              streamingResponseRef.current = message.text // Initialize accumulated text
              const isStreaming = !message.complete
              console.log("🎯 Message streaming status:", { complete: message.complete, isStreaming })
              const streamingId = addMessage({
                role: "assistant",
                content: message.text,
                isStreaming: isStreaming,
                messageKey: message.message_key
              })
              currentStreamingIdRef.current = isStreaming ? streamingId : null
              console.log("🎯 Created new message with ID:", streamingId, "isStreaming:", isStreaming)

              if (message.complete) {
                clearAIStatus()
                incrementMessageCount()
                streamingResponseRef.current = ""
              }
            }
          } else {
            // Continue streaming response - accumulate text
            console.log("🎯 Continuing streaming message:", currentStreamingIdRef.current)
            streamingResponseRef.current += message.text
            // Update message with complete accumulated text by replacing the entire content
            replaceStreamingMessage(currentStreamingIdRef.current, streamingResponseRef.current)
            console.log("🎯 Updated streaming content, total length:", streamingResponseRef.current.length)
          }
        }
        break

      case "audio_output":
      case "direct_audio_output":
        // Handle regular audio output for playback
        // For direct_audio_output, the audio data is nested in message.data.audio_data
        // For audio_output, the audio data is directly in message.data
        let audioData: string | undefined
        let audioFormat: string | undefined
        let audioSource: string | undefined

        if (message.type === "direct_audio_output") {
          // Direct audio output has nested structure
          audioData = message.data?.audio_data
          audioFormat = message.data?.format
          audioSource = message.data?.source
        } else {
          // Regular audio output has flat structure
          audioData = message.data
          audioFormat = message.format
          audioSource = message.source
        }

        console.log("🔊 Audio output received:", {
          type: message.type,
          format: audioFormat,
          dataLength: audioData?.length || 0,
          source: audioSource,
          chunkId: message.chunk_id,
          hasNestedData: !!message.data?.audio_data,
          hasDirectData: !!message.data && typeof message.data === 'string'
        })

        // Validate audio data more thoroughly
        if (audioData && typeof audioData === 'string' && audioData.length > 0) {
          // Ensure audio context is initialized before playback
          initializeAudioContext().then(() => {
            playChunk(audioData, message.chunk_id || `chunk_${Date.now()}`)
            incrementAudioChunkCount()
          }).catch(error => {
            console.error("Failed to initialize audio context for playback:", error)
            handlePlaybackError("Audio playback initialization failed", {
              error,
              chunkId: message.chunk_id,
              dataLength: audioData?.length,
              sessionId: sessionIdRef.current,
              messageType: message.type
            })
          })
        } else {
          console.warn("⚠️ Audio output message has invalid audio data:", {
            hasData: !!message.data,
            hasAudioData: !!message.data?.audio_data,
            dataType: typeof message.data,
            audioDataType: typeof message.data?.audio_data,
            dataLength: audioData?.length || 0,
            messageType: message.type,
            chunkId: message.chunk_id,
            fullMessage: message
          })
        }
        break

      case "audio_output_chunk":
        // Handle chunked audio output with reassembly
        console.log("🧩 Chunked audio output received:", {
          streamId: message.stream_id,
          sequence: message.sequence,
          totalChunks: message.total_chunks,
          dataLength: message.data?.length || 0,
          isComplete: message.is_complete
        })

        if (message.data && message.stream_id !== undefined && message.sequence !== undefined) {
          // Ensure audio context is initialized before playback
          initializeAudioContext().then(() => {
            handleChunkedAudio(
              message.stream_id,
              message.data,
              message.sequence,
              message.total_chunks || 1,
              message.is_complete || false
            )
            incrementAudioChunkCount()
          }).catch(error => {
            console.error("Failed to initialize audio context for chunked playback:", error)
            handlePlaybackError("Chunked audio playback initialization failed", {
              error,
              streamId: message.stream_id,
              sequence: message.sequence,
              dataLength: message.data?.length,
              sessionId: sessionIdRef.current
            })
          })
        } else {
          console.warn("⚠️ Chunked audio output message missing required data")
        }
        break

      case "audio_playback_stop":
        // Handle audio playback stop command
        console.log("🛑 Audio playback stop received")
        handleSpeechInterruption()
        break

      case "manual_speech_end_ack":
        // Handle manual speech end acknowledgment
        console.log("✅ Manual speech end acknowledged")
        updateTranscriptStatus("Speech end confirmed")
        break

      case "speech_started":
        console.log("🎤 Speech started detected")
        updateTranscriptStatus("Speech detected...")
        // Interrupt any current audio playback
        handleSpeechInterruption()
        break

      case "speech_stopped":
        console.log("🎤 Speech stopped detected")
        updateTranscriptStatus("Processing speech...")
        break

      case "interruption":
      case "control_interruption":
        console.log("🚨 Interruption:", message.status, message.interruption_type, "type:", message.type)
        if (message.status === "start") {
          updateAIStatus(`AI interruption: ${message.interruption_type}`)
          // Stop any current audio playback
          handleSpeechInterruption()
        } else if (message.status === "end") {
          clearAIStatus()
          updateTranscriptStatus("Ready for speech...")
        }
        break

      case "mode_selection":
        console.log("🎯 Mode selection confirmed:", message.mode)
        addMessage({
          role: "system",
          content: `Mode switched to: ${message.mode}`
        })
        break

      case "cooperative_interjection":
        console.log("🤝 Cooperative interjection:", message.content)
        // These are brief AI interjections that don't interrupt the conversation flow
        toast.info(message.content || message.text, { duration: 3000 })
        break

      case "audio_control":
        console.log("🎤 Audio control:", message.action, message.reason)
        if (message.action === "stop_recording") {
          updateTranscriptStatus(`Recording paused: ${message.reason}`)
        } else if (message.action === "resume_recording") {
          updateTranscriptStatus("Ready for speech...")
        }
        break

      case "ping":
        // Handle ping from server - respond with pong
        console.log("📡 Ping received from server")
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          wsRef.current.send(JSON.stringify({
            type: "pong",
            timestamp: Date.now(),
            original_timestamp: message.timestamp,
            session_id: sessionIdRef.current
          }))
          console.log("📡 Pong sent in response to server ping")
        }
        break

      case "pong":
        // Handle heartbeat pong response
        const pingTime = lastPingTimeRef.current
        const pongTime = Date.now()
        const latency = pongTime - pingTime

        console.log("📡 Heartbeat pong received, latency:", latency + "ms")

        // Update connection quality based on latency
        if (latency < 100) {
          updateConnectionQuality("good")
        } else if (latency < 300) {
          updateConnectionQuality("poor")
        } else {
          updateConnectionQuality("unstable")
        }
        break

      case "connection_quality":
        // Handle connection quality updates from server
        console.log("📊 Connection quality update:", message.quality)
        if (message.quality) {
          updateConnectionQuality(message.quality === "error" ? "error" : message.quality)
        }
        break

      case "error":
        console.error("❌ Server error:", message.message)
        handleConnectionError(`Server error: ${message.message}`, {
          serverMessage: message.message,
          errorCode: message.code,
          sessionId: sessionIdRef.current,
          timestamp: new Date().toISOString()
        })
        break

      default:
        console.log("❓ Unknown message type:", message.type, message)
      }
    } catch (error) {
      console.error("❌ Error processing WebSocket message:", error, "message:", message)
      handleConnectionError(`Message processing error: ${error}`, {
        error,
        messageType: message.type,
        sessionId: sessionIdRef.current,
        timestamp: new Date().toISOString()
      })
    }
  }, [
    addMessage,
    updateStreamingMessage,
    completeStreamingMessage,
    updateTranscriptStatus,
    updateAIStatus,
    clearTranscriptStatus,
    clearAIStatus,
    currentStreamingMessageId,
    playChunk,
    handleSpeechInterruption,
    initializeAudioContext,
    incrementMessageCount,
    incrementAudioChunkCount
  ])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) clearTimeout(reconnectTimeoutRef.current)
    wsRef.current?.close(1000, "Client disconnect")
    sessionIdRef.current = null
    reconnectAttemptsRef.current = 0
  }, [])

  const sendData = useCallback((data: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(data))
    } else {
      // Queue message for later sending
      messageQueueRef.current.push(data)
      updateMessageQueueSize(messageQueueRef.current.length)
      console.log("📦 Queued message for later sending:", data.type)
    }
  }, [updateMessageQueueSize])

  useEffect(() => {
    connect()
    return () => {
      disconnect()
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, []) // Remove dependencies to prevent infinite loop

  // Helper function to convert ArrayBuffer to base64
  const arrayBufferToBase64 = useCallback((buffer: ArrayBuffer): string => {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }, [])

  // Helper methods for sending specific message types
  const sendAudioData = useCallback((audioData: ArrayBuffer) => {
    // Convert ArrayBuffer to base64 to match backend expectations
    const base64Audio = arrayBufferToBase64(audioData)
    sendData({
      type: "audio_input", // Match the backend expected type
      data: base64Audio,
      format: "pcm16",
      sample_rate: config.audio.sampleRate,
      session_id: sessionIdRef.current,
      timestamp: new Date().toISOString()
    })
    incrementAudioChunkCount()
  }, [sendData, arrayBufferToBase64, config.audio.sampleRate, incrementAudioChunkCount])

  const sendModeSelection = useCallback((mode: string) => {
    sendData({
      type: "mode_selection",
      mode: mode,
      session_id: sessionIdRef.current,
      timestamp: new Date().toISOString()
    })
  }, [sendData])

  const sendManualSpeechEnd = useCallback(() => {
    sendData({
      type: "manual_speech_end",
      session_id: sessionIdRef.current,
      timestamp: new Date().toISOString(),
      reason: "user_manual_stop"
    })
  }, [sendData])

  return {
    sendData,
    sendAudioData,
    sendModeSelection,
    sendManualSpeechEnd,
    disconnect,
    sessionId: sessionIdRef.current,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN,
    currentSession
  }
}
