/**
 * AudioWorklet Processor for Real-time Audio Processing
 * Provides advanced audio processing capabilities for Audio Agent
 * Based on the sophisticated system from the old frontend
 */

class RealtimeAudioProcessor extends AudioWorkletProcessor {
  constructor() {
    super();

    // Audio processing parameters - optimized for low latency
    this.bufferSize = 2048;
    this.inputBuffer = new Float32Array(this.bufferSize);
    this.outputBuffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;

    // Audio enhancement settings
    this.gainControl = 1.0;
    this.noiseGate = 0.01;
    this.compressionRatio = 2.0;
    this.isEnabled = true;

    // Processing statistics
    this.processedSamples = 0;
    this.droppedFrames = 0;
    this.peakLevel = 0;
    this.rmsLevel = 0;

    // Performance monitoring
    this.processingTime = 0;
    this.lastStatsTime = 0;
    this.statsInterval = 1000; // 1 second

    // Listen for parameter changes from main thread
    this.port.onmessage = (event) => {
      this.handleMessage(event.data);
    };

    // Send ready signal
    this.port.postMessage({
      type: "processor-ready",
      timestamp: currentTime,
    });
  }

  /**
   * Handle messages from main thread
   * @param {Object} data Message data
   */
  handleMessage(data) {
    switch (data.type) {
      case "set-gain":
        this.gainControl = Math.max(0, Math.min(2.0, data.value));
        break;

      case "set-noise-gate":
        this.noiseGate = Math.max(0, Math.min(1.0, data.value));
        break;

      case "set-compression":
        this.compressionRatio = Math.max(1.0, Math.min(10.0, data.value));
        break;

      case "toggle-processing":
        this.isEnabled = data.enabled;
        break;

      case "get-stats":
        this.sendStats();
        break;

      case "reset-stats":
        this.resetStats();
        break;

      default:
        console.warn("Unknown message type:", data.type);
    }
  }

  /**
   * Main audio processing method
   * @param {Float32Array[][]} inputs Input audio data
   * @param {Float32Array[][]} outputs Output audio data
   * @param {Object} parameters AudioParam values
   * @returns {boolean} Keep processor alive
   */
  process(inputs, outputs, parameters) {
    const startTime = performance.now();

    const input = inputs[0];
    const output = outputs[0];

    // Ensure we have valid input and output
    if (!input || !output || input.length === 0 || output.length === 0) {
      return true;
    }

    const inputChannel = input[0];
    const outputChannel = output[0];

    if (!inputChannel || !outputChannel) {
      return true;
    }

    try {
      if (this.isEnabled) {
        // Process audio with enhancements
        this.processAudioWithEnhancements(inputChannel, outputChannel);
      } else {
        // Pass through without processing
        outputChannel.set(inputChannel);
      }

      // Update statistics
      this.processedSamples += inputChannel.length;
      this.updateAudioLevels(inputChannel);

      // Send periodic stats
      const now = currentTime * 1000;
      if (now - this.lastStatsTime > this.statsInterval) {
        this.sendStats();
        this.lastStatsTime = now;
      }
    } catch (error) {
      this.droppedFrames++;
      console.error("Audio processing error:", error);
      // Fallback to pass-through
      outputChannel.set(inputChannel);
    }

    // Track processing time
    this.processingTime = performance.now() - startTime;

    return true;
  }

  /**
   * Process audio with enhancements
   * @param {Float32Array} input Input audio samples
   * @param {Float32Array} output Output audio samples
   */
  processAudioWithEnhancements(input, output) {
    for (let i = 0; i < input.length; i++) {
      let sample = input[i];

      // Apply noise gate
      if (Math.abs(sample) < this.noiseGate) {
        sample = 0;
      }

      // Apply gain control
      sample *= this.gainControl;

      // Apply compression
      if (Math.abs(sample) > 0.5) {
        const sign = sample >= 0 ? 1 : -1;
        const compressed = Math.pow(
          Math.abs(sample),
          1 / this.compressionRatio
        );
        sample = sign * compressed;
      }

      // Prevent clipping
      sample = Math.max(-1, Math.min(1, sample));

      output[i] = sample;
    }
  }

  /**
   * Update audio level measurements
   * @param {Float32Array} samples Audio samples
   */
  updateAudioLevels(samples) {
    let sum = 0;
    let peak = 0;

    for (let i = 0; i < samples.length; i++) {
      const abs = Math.abs(samples[i]);
      sum += abs * abs;
      peak = Math.max(peak, abs);
    }

    this.peakLevel = peak;
    this.rmsLevel = Math.sqrt(sum / samples.length);
  }

  /**
   * Send statistics to main thread
   */
  sendStats() {
    this.port.postMessage({
      type: "audio-stats",
      stats: {
        processedSamples: this.processedSamples,
        droppedFrames: this.droppedFrames,
        peakLevel: this.peakLevel,
        rmsLevel: this.rmsLevel,
        processingTime: this.processingTime,
        gainControl: this.gainControl,
        noiseGate: this.noiseGate,
        compressionRatio: this.compressionRatio,
        isEnabled: this.isEnabled,
        timestamp: currentTime,
      },
    });
  }

  /**
   * Reset processing statistics
   */
  resetStats() {
    this.processedSamples = 0;
    this.droppedFrames = 0;
    this.peakLevel = 0;
    this.rmsLevel = 0;
    this.processingTime = 0;
  }
}

// Register the processor
registerProcessor("realtime-audio-processor", RealtimeAudioProcessor);
